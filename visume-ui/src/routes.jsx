import React from "react";

// Auth Imports
import WaitList from "components/waitlist/WaitList";
import SignIn from "views/auth/SignIn";
import EmpSignIn from "views/auth/EmpSignIn";
import Dashboard from "views/candidate/Dashboard/Dashboard";
import { EmployerDashboard } from "views/employer/EmployerDashboard";
import ManageCredits from "./views/employer/settings/ManageCredits"
import BusinessAssociate from "views/partner/BusinessAssociate";
import CandidateSettings from "./views/candidate/candidateSettings.jsx"
// Icon Imports
import {
  MdVideoFile,
  MdBarChart,
  MdPerson,
  MdHome,
  MdSearch,
  MdOutlineSettings,
  MdSpaceDashboard,
  MdSettings,
} from "react-icons/md";
import TrackCandidates from "views/employer/TrackCandidates";
import AllVideoProfiles from "views/candidate/AllVideoProfiles";
import Suggestedjobs from "views/candidate/SuggestedJobs";
import SuggestedCandidates from "views/employer/SuggestedCandidates";
import { HiOutlineBriefcase, HiOutlineSparkles } from "react-icons/hi";
import AdminDashboard from "./views/Admin/AdminDashboard";
import CandidateProfiles from "./views/Admin/CandidateProfiles";
import EmployerProfiles from "./views/Admin/EmployerProfiles";
import CandidatePlans from "./views/Admin/CandidatePlans";
import VideoProfiles from "views/Admin/VideoProfiles";

import JobDescriptionCandidates from "./views/employer/EmployerDashboard/JobDescriptionCandidates";

const routes = [
  {
    name: "WaitList",
    layout: "/auth",
    path: "/",
    component: <WaitList />,
  },
  {
    name: "Home",
    layout: "/candidate",
    path: "dashboard",
    icon: <MdHome className="h-6 w-6" />,
    component: <Dashboard />,
  },
  {
    name: "Suggested Jobs",
    layout: "/candidate",
    path: "suggested-jobs",
    icon: <HiOutlineSparkles className="h-6 w-6" />,
    component: <Suggestedjobs />,
  },
  {
    name: "My Visumes",
    layout: "/candidate",
    path: "video-resume",
    icon: <MdVideoFile className="h-6 w-6" />,
    component: <AllVideoProfiles />,
  },
  {
    name: "Settings",
    layout: "/candidate",
    path: "settings",
    icon: <MdSettings className="h-6 w-6" />,
    component: <CandidateSettings />,
  },
  // Admin routes
  {
    name: "Dashboard",
    layout: "/admin",
    path: "dashboard",
    icon: <MdVideoFile className="h-6 w-6" />,
    component: <AdminDashboard />,
  },
  { 
    name:"Candidate Profiles",
    layout: "/admin",
    path: "candidateProfiles",
    icon: <MdVideoFile className="h-6 w-6" />,
    component: <CandidateProfiles />,
  },
  { 
    name:"Employer Profiles",
    layout: "/admin",
    path:"employerProfiles",
    icon:<MdVideoFile className="h-6 w-6" />,
    component: <EmployerProfiles />,
  },
  { 
    name:"Video Profiles",
    layout: "/admin",
    path:"videoProfiles",
    icon:<MdVideoFile className="h-6 w-6" />,
    component: <VideoProfiles />,
  },
  { 
    name:"Plans",
    layout: "/admin",
    path:"candidatePlans",
    icon:<MdVideoFile className="h-6 w-6" />,
    component: <CandidatePlans />
  },
  // Employer routes
  {
    name: "Home",
    layout: "/employer",
    path: "dashboard",
    icon: <MdHome className="h-6 w-6" />,
    component: <EmployerDashboard />,
  },
  {
    name: "Profile Search",
    layout: "/employer",
    path: "profile-search",
    icon: <MdSearch className="h-6 w-6" />,
    component: <EmployerDashboard />,
  },
  {
    name: "Track Candidates",
    layout: "/employer",
    path: "track-candidates/*",
    icon: <MdBarChart className="h-6 w-6" />,
    component: <TrackCandidates />,
  },
  {
    name: "Candidate Profiles",
    layout: "/employer",
    path: "suggested-candidates",
    icon: <HiOutlineBriefcase className="h-5 w-6" />,
    component: <SuggestedCandidates />,
  },
  {
    name: "Settings",
    layout: "/employer",
    path: "settings/*",
    icon: <MdSettings className="h-6 w-6" />,
    component: <ManageCredits />,
  },
  {
    name: "Job Description Candidates",
    layout: "/employer",
    path: "job-description/:id",
    icon: <MdPerson className="h-6 w-6" />,
    component: <JobDescriptionCandidates />,
    sidebar: false, // Hide from sidebar
  },
  {
    name: "Business Associate",
    layout: "/business-associate",
    path: "dashboard",
    icon: <MdBarChart className="h-6 w-6" />,
    component: <BusinessAssociate />,
  },
];

export default routes;
