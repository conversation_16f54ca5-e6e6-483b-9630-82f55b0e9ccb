import Footer from "components/footer/FooterAuthDefault";
import authImg from "assets/img/auth/auth.png";
import { Link, Routes, Route, Navigate, useLocation } from "react-router-dom";
import routes from "routes";
import FixedPlugin from "components/fixedPlugin/FixedPlugin";
import { Code, Play } from "lucide-react";

export default function Auth() {
  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/auth") {
        return (
          <Route path={`/${prop.path}`} element={prop.component} key={key} />
        );
      } else {
        return null;
      }
    });
  };
  document.documentElement.dir = "ltr";
  const location = useLocation();
  const showLink = false
  return (
    <div>
      <div className="relative float-right h-full min-h-screen w-full  !bg-white dark:!bg-navy-900">
        <FixedPlugin />
        <main className={`mx-auto min-h-screen `}>
          <div className="relative flex">
            <div className="mx-auto flex min-h-full w-full flex-col justify-start pt-12 md:max-w-[75%]  lg:max-w-[1013px] lg:px-8 lg:pt-0 xl:min-h-[100vh] xl:max-w-[1383px] xl:px-0 xl:pl-[70px]">
              <div className=" mb-auto flex flex-col pl-5 pr-5 md:pl-12 md:pr-0 lg:max-w-[48%] lg:pl-0 xl:max-w-full">
                {/* {showLink ? (
                  <Link to="/auth/signin" className="mt-10 w-max">
                    <div className="mx-auto flex h-fit w-fit items-center hover:cursor-pointer">
                      <svg
                        width="8"
                        height="12"
                        viewBox="0 0 8 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.70994 2.11997L2.82994 5.99997L6.70994 9.87997C7.09994 10.27 7.09994 10.9 6.70994 11.29C6.31994 11.68 5.68994 11.68 5.29994 11.29L0.709941 6.69997C0.319941 6.30997 0.319941 5.67997 0.709941 5.28997L5.29994 0.699971C5.68994 0.309971 6.31994 0.309971 6.70994 0.699971C7.08994 1.08997 7.09994 1.72997 6.70994 2.11997V2.11997Z"
                          fill="#A3AED0"
                        />
                      </svg>
                      <p className="ml-3 text-sm text-gray-600">
                        Back to SignIn
                      </p>
                    </div>
                  </Link>
                ) : (
                  <Link to="/" className="mt-10 w-max">
                    <div className="mx-auto flex h-fit w-fit items-center hover:cursor-pointer">
                      <svg
                        width="8"
                        height="12"
                        viewBox="0 0 8 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.70994 2.11997L2.82994 5.99997L6.70994 9.87997C7.09994 10.27 7.09994 10.9 6.70994 11.29C6.31994 11.68 5.68994 11.68 5.29994 11.29L0.709941 6.69997C0.319941 6.30997 0.319941 5.67997 0.709941 5.28997L5.29994 0.699971C5.68994 0.309971 6.31994 0.309971 6.70994 0.699971C7.08994 1.08997 7.09994 1.72997 6.70994 2.11997V2.11997Z"
                          fill="#A3AED0"
                        />
                      </svg>
                      <p className="ml-3 text-sm text-gray-600">
                        Back to Dashboard
                      </p>
                    </div>
                  </Link>
                )} */}
                <Routes>
                  {getRoutes(routes)}
                  <Route
                    path="/"
                    element={<Navigate to="/candidate/signIn" replace />}
                  />
                </Routes>
                {/* <div className="absolute right-0 block h-full min-h-screen sm:hidden md:block lg:block lg:w-[49vw] xl:block 2xl:w-[44vw]">
                  <div
                    className="absolute flex h-full w-full items-end justify-center bg-cover bg-center lg:rounded-bl-[120px] xl:rounded-bl-[200px]"
                    style={{ backgroundImage: `url(${authImg})` }}
                  />
                </div> */}
                <div className="absolute right-0 top-0  mr-5 mt-10 hidden h-[90vh] w-full overflow-hidden rounded-lg bg-gradient-to-br from-brand-500 to-brand-600 p-8 text-white dark:bg-brand-700 md:hidden md:w-1/2 lg:block lg:max-w-2xl">
                  <div className="relative h-full">
                    <div className="mb-8 flex items-center">
                      <Code className="h-8 w-8" />
                      <span className="ml-2 text-xl font-bold">Visume.ai</span>
                    </div>
                    <div className="mb-12">
                      <h2 className="mb-4 text-5xl font-extrabold leading-tight">
                        <span className="font-light"> Create your</span>
                        <br />
                        Video Resume
                        <br />
                        <span className="font-light italic"> in 5 mins.</span>
                      </h2>
                      <p className="text-xl text-white/80">
                        Reach 100's of recruiters with a single{" "}
                        <span className="font-bold">Visume.</span>
                        <br />
                        Create to get discovered!
                      </p>
                    </div>
                    <button className="group flex items-center rounded-full bg-white px-6 py-3 text-lg font-semibold text-brand-600 shadow-lg transition-all hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-brand-600">
                      <Play className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
                      Watch demo
                    </button>
                    <div className="absolute bottom-0 right-0 -mb-16 -mr-16 h-64 w-64 rounded-full bg-white/20 blur-3xl" />
                    <div className="absolute left-0 top-1/2 -ml-16 -mt-16 h-32 w-32 rounded-full bg-white/20 blur-2xl" />
                  </div>
                </div>
              </div>
              {/* <Footer /> */}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
