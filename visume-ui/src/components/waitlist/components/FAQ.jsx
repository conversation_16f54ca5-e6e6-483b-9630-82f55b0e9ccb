"use client"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Plus, Minus } from "lucide-react"

const FAQ = () => {
  const [expandedIndex, setExpandedIndex] = useState(null)

  const faqs = [
  {
    question: "What makes Visume different from traditional resumes or intro videos?",
    answer:
      "Visume replaces outdated resumes and awkward self-introductions with an AI-led interview tailored to the job. It records your answers via webcam and automatically creates a professional, polished video resume — no editing or writing needed.",
  },
  {
    question: "Why do most candidates get rejected before interviews?",
    answer:
      "Many candidates get filtered out by Applicant Tracking Systems (ATS) or lost among lookalike resumes. Visume solves this by letting you showcase your real communication, problem-solving, and personality through an AI-generated video interview — no text resume required.",
    highlighted: true,
  },
  {
    question: "How does Visume help jobseekers stand out?",
    answer:
      "By answering a few job-specific questions, jobseekers create a 2-minute Visume that reveals who they are, not just what they’ve done. This helps recruiters instantly assess fit — without needing to read through generic resumes.",
  },
  {
    question: "What do employers gain from using Visume?",
    answer:
      "Employers can upload a job description and instantly receive AI-matched, role-specific video interviews. They see real communication, thinking, and confidence — not just bullet points. It shortens hiring cycles and improves quality of hire.",
  },
  {
    question: "Do I need to edit or write anything for my Visume?",
    answer:
      "Not at all. You simply answer questions asked by the AI. Visume handles the recording, editing, formatting, and publishing — giving you a compelling, shareable video resume tailored to the job role.",
  },
]


  return (
    <section className="py-10 lg:py-14 bg-white relative">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-12 lg:grid-cols-3">
          {/* Left Column - FAQ Header */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm"
              style={{ fontFamily: "Sora, sans-serif" }}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              FAQ
            </motion.div>
            <h2
              className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
              style={{ fontFamily: "Manrope, sans-serif" }}
            >
              Everything you need to{" "}
              <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">know</span>
            </h2>
            <p className="text-slate-600 leading-relaxed text-lg" style={{ fontFamily: "Sora, sans-serif" }}>
              Can't find the answer you're looking for? Reach out to our support team and get help with Visume.
            </p>
          </motion.div>

          {/* Right Column - FAQ Items */}
          <div className="space-y-4 lg:col-span-2">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                className={`group relative overflow-hidden rounded-2xl border transition-all duration-500 ${
                  faq.highlighted
                    ? "border-blue-300/60 bg-gradient-to-br from-blue-50/80 to-indigo-50/40 shadow-xl shadow-blue-200/25"
                    : "border-blue-200/40 bg-white shadow-lg shadow-blue-200/20 hover:border-blue-300/60 hover:shadow-xl hover:shadow-blue-300/25"
                }`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ y: -2 }}
              >
                <motion.button
                  className="flex w-full items-center justify-between px-6 py-6 text-left transition-all duration-200"
                  onClick={() => setExpandedIndex(expandedIndex === index ? null : index)}
                  whileTap={{ scale: 0.995 }}
                >
                  <h3
                    className="pr-8 text-lg font-bold text-slate-900 leading-tight group-hover:text-blue-700 transition-colors duration-300"
                    style={{ fontFamily: "Manrope, sans-serif" }}
                  >
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{
                      rotate: expandedIndex === index ? 45 : 0,
                    }}
                    transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
                    className="flex-shrink-0"
                  >
                    {expandedIndex === index ? (
                      <Minus className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Plus className="h-5 w-5 text-blue-600" />
                    )}
                  </motion.div>
                </motion.button>

                <AnimatePresence>
                  {expandedIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.4, ease: "easeInOut" }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 pt-2">
                        <motion.div
                          initial={{ y: -10, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ delay: 0.1, duration: 0.3 }}
                          className="border-t border-slate-200/60 pt-4"
                        >
                          <p
                            className="text-slate-600 leading-relaxed text-base"
                            style={{ fontFamily: "Sora, sans-serif" }}
                          >
                            {faq.answer}
                          </p>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Subtle Gradient Overlay */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/10 via-transparent to-indigo-50/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100 pointer-events-none"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
      `}</style>
    </section>
  )
}

export default FAQ
