"use client"
import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

const HowItWorks = () => {
  const [activeTab, setActiveTab] = useState("jobSeeker")
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkIfMobile()
    window.addEventListener("resize", checkIfMobile)
    return () => window.removeEventListener("resize", checkIfMobile)
  }, [])

  const tabData = {
    jobSeeker: {
      title: "For Job Seekers",
      steps: [
        {
          step: 1,
          title: "AI Creates Your Profile",
          description:
            "Just answer our AI questions — Visume handles the rest. Upload your resume and let AI build your professional profile automatically.",
          image: "/forJobSeekers/createprofile.png",
        },
        {
          step: 2,
          title: "Record Video Resume",
          description:
            "Show who you are, not just what you've done. Create compelling video introductions that reveal your personality and passion.",
          image: "/forJobSeekers/recordvideoresume.png",
        },
        {
          step: 3,
          title: "Get Matched to Right Jobs",
          description:
            "Get matched to jobs where you're the right fit. Our AI connects you with opportunities that align with your skills and values.",
          image: "/forJobSeekers/applyjob.png",
        },
        {
          step: 4,
          title: "Stand Out & Get Hired",
          description:
            "Stand out with role-specific video resumes instead of PDFs. Build meaningful connections with employers who value your uniqueness.",
          image: "/forJobSeekers/connectwithemployers.png",
        },
      ],
    },
    hiringManager: {
      title: "For Employers",
      steps: [
        {
          step: 1,
          title: "Upload Job Description",
          description:
            "Upload a JD and get AI-matched video interviews. Create detailed job descriptions that attract the right talent to your pool.",
          icon: (
            <img
              src="/forHiringManagers/createjob.png"
              alt="Post Job Description"
              className="drop-shadow-blue h-full w-full object-contain"
            />
          ),
        },
        {
          step: 2,
          title: "Browse Video Interviews",
          description:
            "See communication, confidence, and problem-solving up front. Browse authentic video introductions that reveal cultural fit.",
          icon: (
            <img
              src="/forHiringManagers/recievevideoapplications.png"
              alt="Browse Video Resumes"
              className="drop-shadow-blue h-full w-full object-contain"
            />
          ),
        },
        {
          step: 3,
          title: "No More Guesswork",
          description:
            "No more guesswork from bullet-point resumes. Quickly assess real communication skills and candidate enthusiasm.",
          icon: (
            <img
              src="/forHiringManagers/screening.png"
              alt="Screen Efficiently"
              className="drop-shadow-blue h-full w-full object-contain"
            />
          ),
        },
        {
          step: 4,
          title: "Hire Faster",
          description:
            "Hire faster with pre-screened, high-context profiles. Interview qualified candidates who demonstrate genuine interest and fit.",
          icon: (
            <img
              src="/forHiringManagers/hirebetter.png"
              alt="Make Better Hires"
              className="drop-shadow-blue h-full w-full object-contain"
            />
          ),
        },
      ],
    },
  }

  const isJobSeekerStep = (s) => s.image !== undefined
  const isHiringManagerStep = (s) => s.icon !== undefined

  return (
    <section className="py-10 lg:py-14 bg-white relative" id="how-it-works">
      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm"
            style={{ fontFamily: "Sora, sans-serif" }}
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            How It Works
          </motion.div>
          <h2
            className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            Simple Steps to
            <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text"> Success</span>
          </h2>
          <p
            className="text-slate-600 mx-auto max-w-3xl text-lg leading-relaxed"
            style={{ fontFamily: "Sora, sans-serif" }}
          >
            Whether you're looking for your next opportunity or searching for the perfect candidate, our platform makes
            the process seamless and effective.
          </p>
        </motion.div>

        {/* Smaller, More Subtle Tab Switcher */}
        <motion.div
          className="mb-12 flex justify-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="relative inline-flex rounded-lg bg-slate-100/50 border border-slate-200/40 p-0.5 shadow-sm">
            <motion.div
              className="absolute top-0.5 bottom-0.5 rounded-md bg-blue-500 shadow-sm"
              animate={{
                x: activeTab === "jobSeeker" ? 0 : "100%",
              }}
              transition={{ duration: 0.25, ease: "easeInOut" }}
              style={{
                width: "calc(50% - 0.125rem)",
              }}
            />
            {Object.entries(tabData).map(([key, data]) => (
              <button
                key={key}
                onClick={() => setActiveTab(key)}
                className={`relative z-10 px-4 py-1.5 text-sm font-medium transition-all duration-200 rounded-md whitespace-nowrap ${
                  activeTab === key ? "text-white" : "text-slate-600 hover:text-slate-800"
                }`}
                style={{
                  fontFamily: "Manrope, sans-serif",
                  width: "140px",
                }}
              >
                {data.title}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Mobile Scroll Indicator */}
        {isMobile && (
          <motion.div
            className="text-slate-500 border-slate-200/60 mx-auto mb-6 flex w-fit items-center justify-center rounded-full border bg-white/60 backdrop-blur-sm px-4 py-2.5 text-sm font-medium shadow-sm"
            style={{ fontFamily: "Sora, sans-serif" }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="mx-3">Swipe to explore steps</span>
            <ChevronRight className="h-4 w-4" />
          </motion.div>
        )}

        {/* Modern Steps Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className={`${
              isMobile
                ? "scrollbar-hide flex snap-x snap-mandatory space-x-5 overflow-x-auto pb-6"
                : "grid gap-6 md:grid-cols-2 lg:grid-cols-4"
            }`}
          >
            {tabData[activeTab].steps.map((step, index) => (
              <motion.div
                key={step.step}
                className={`relative ${isMobile ? "w-80 flex-none snap-center" : ""} group`}
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  delay: index * 0.15,
                  duration: 0.8,
                  ease: "easeOut",
                }}
              >
                {/* Modern Connecting Line for Desktop */}
                {!isMobile && index < tabData[activeTab].steps.length - 1 && (
                  <motion.div
                    className="absolute left-1/2 top-28 z-0 hidden h-px w-full bg-gradient-to-r from-transparent via-blue-300/60 to-transparent lg:block"
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ delay: index * 0.15 + 0.5, duration: 0.8 }}
                  />
                )}

                {/* Reduced Blue Gradient Shadow */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/15 via-indigo-500/10 to-blue-600/15 rounded-2xl blur-lg transform translate-x-1 translate-y-1"></div>

                {/* Modern SaaS Card with Reduced Shadow */}
                <div className="relative h-full rounded-2xl bg-white border border-blue-200/40 p-6 shadow-lg shadow-blue-500/15 transition-all duration-500 group-hover:-translate-y-3 hover:shadow-xl hover:shadow-blue-500/25 hover:border-blue-300/60">
                  {/* Bigger Icon Container */}
                  <div className="relative z-10 mb-6 flex justify-center">
                    {isJobSeekerStep(step) ? (
                      <motion.div
                        className="flex h-44 w-44 items-center justify-center"
                        whileHover={{ scale: 1.05, rotate: 2 }}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 15,
                        }}
                      >
                        <img
                          src={step.image || "/placeholder.svg"}
                          alt={step.title}
                          className="h-full w-full object-contain transition-all duration-300 group-hover:scale-110 drop-shadow-lg"
                        />
                      </motion.div>
                    ) : isHiringManagerStep(step) ? (
                      <motion.div
                        className="flex h-44 w-44 items-center justify-center"
                        whileHover={{ scale: 1.05, rotate: -2 }}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 15,
                        }}
                      >
                        <div className="h-full w-full transition-all duration-300 group-hover:scale-110">
                          {step.icon}
                        </div>
                      </motion.div>
                    ) : null}
                  </div>

                  {/* Content with Left-Aligned Text */}
                  <div className="text-left">
                    <h3
                      className="text-slate-900 mb-3 text-lg font-bold leading-tight transition-colors duration-300 group-hover:text-blue-700"
                      style={{ fontFamily: "Manrope, sans-serif" }}
                    >
                      {step.title}
                    </h3>
                    <p
                      className="text-slate-600 text-sm leading-relaxed text-left"
                      style={{ fontFamily: "Sora, sans-serif" }}
                    >
                      {step.description}
                    </p>
                  </div>

                  {/* Subtle Gradient Overlay */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/20 via-transparent to-indigo-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .drop-shadow-blue {
          filter: drop-shadow(0 4px 12px rgba(59, 130, 246, 0.15));
        }
        .drop-shadow-blue-lg {
          filter: drop-shadow(0 8px 20px rgba(59, 130, 246, 0.25));
        }
        @media (max-width: 768px) {
          .snap-x {
            scroll-snap-type: x mandatory;
          }
          .snap-center {
            scroll-snap-align: center;
          }
        }
      `}</style>
    </section>
  )
}

export default HowItWorks
