"use client"
import { motion } from "framer-motion"
import {
  Zap,
  Target,
  BarChart3,
  Users,
  TrendingUp,
  Cpu,
  Lightbulb,
  Heart,
  Video,
  GitBranch,
  Sparkles,
} from "lucide-react"

const MovingTags = () => {
  const tags = [
    {
      text: "Video Resumes",
      icon: Video,
    },
    {
      text: "Smart Hiring",
      icon: Cpu,
    },
    {
      text: "AI Matching",
      icon: Zap,
    },
    {
      text: "Cultural Fit",
      icon: Heart,
    },
    {
      text: "Talent Acquisition",
      icon: Target,
    },
    {
      text: "HR Tech",
      icon: Sparkles,
    },
    {
      text: "Recruitment Innovation",
      icon: Lightbulb,
    },
    {
      text: "Candidate Experience",
      icon: Users,
    },
    {
      text: "Hiring Efficiency",
      icon: TrendingUp,
    },
    {
      text: "Video Interviewing",
      icon: Video,
    },
    {
      text: "Talent Pipeline",
      icon: GitBranch,
    },
    {
      text: "Modern Recruiting",
      icon: BarChart3,
    },
  ]

  // Duplicate tags for seamless loop
  const duplicatedTags = [...tags, ...tags]

  return (
    <section
      className="py-16 bg-white relative overflow-hidden"
      style={{
        backgroundImage: `
          linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
          linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px)
        `,
        backgroundSize: "24px 24px",
      }}
    >
      {/* Moving Tags Container */}
      <div className="relative">
        <motion.div
          className="flex space-x-6"
          animate={{ x: ["0%", "-50%"] }}
          transition={{
            duration: 45,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
          style={{ width: "200%" }}
        >
          {duplicatedTags.map((tag, index) => {
            const IconComponent = tag.icon
            return (
              <motion.div
                key={`${tag.text}-${index}`}
                className="flex min-w-fit items-center gap-3 whitespace-nowrap rounded-2xl bg-gradient-to-r from-slate-50 to-blue-50/50 border border-slate-200/60 px-5 py-3.5 text-sm font-medium text-slate-700 backdrop-blur-sm"
                style={{ fontFamily: "Sora, sans-serif" }}
                whileHover={{
                  scale: 1.08,
                  y: -3,
                  background: "linear-gradient(135deg, #f8fafc 0%, #dbeafe 100%)",
                  borderColor: "rgba(59, 130, 246, 0.4)",
                }}
                transition={{ type: "spring", stiffness: 400, damping: 15 }}
              >
                <IconComponent size={18} className="text-blue-500" />
                {tag.text}
              </motion.div>
            )
          })}
        </motion.div>

        {/* Gradient fade edges */}
        <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-white via-white/80 to-transparent pointer-events-none z-10"></div>
        <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-white via-white/80 to-transparent pointer-events-none z-10"></div>
      </div>
    </section>
  )
}

export default MovingTags
