import React from 'react';
import PropTypes from 'prop-types';

class ImageErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Image Error:', error);
    console.error('Error Info:', errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
          <div className="text-center">
            <p className="text-gray-600 mb-2">Failed to load image</p>
            {this.props.retry && (
              <button
                onClick={() => {
                  this.setState({ hasError: false, error: null });
                  this.props.retry();
                }}
                className="text-blue-500 hover:text-blue-700 font-medium"
              >
                Retry
              </button>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

ImageErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired,
  fallback: PropTypes.node,
  retry: PropTypes.func,
  onError: PropTypes.func
};

export default ImageErrorBoundary;