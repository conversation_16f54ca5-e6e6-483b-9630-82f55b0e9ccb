import React from 'react';
import { HiUser, HiAcademicCap, HiB<PERSON>case, Hi<PERSON>ightBulb, HiBadgeCheck } from 'react-icons/hi';

const ResumePreview = ({ resumeData, isCompact = false }) => {
  if (!resumeData) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No resume data available</p>
      </div>
    );
  }

  if (isCompact) {
    // Compact view for dropdown selection
    return (
      <div className="bg-gray-50 rounded-lg p-4 mt-2 max-h-60 overflow-y-auto">
        <div className="space-y-3">
          {/* Personal Info */}
          <div className="flex items-center gap-2">
            <HiUser className="text-blue-600 flex-shrink-0" />
            <div className="min-w-0">
              <p className="font-medium text-sm truncate">{resumeData.personal_info?.name || 'Name not available'}</p>
              <p className="text-xs text-gray-600 truncate">{resumeData.personal_info?.email || 'Email not available'}</p>
            </div>
          </div>

          {/* Skills Preview */}
          {resumeData.skills && resumeData.skills.length > 0 && (
            <div className="flex items-start gap-2">
              <HiLightBulb className="text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="min-w-0">
                <p className="text-xs font-medium text-gray-700 mb-1">Skills:</p>
                <div className="flex flex-wrap gap-1">
                  {resumeData.skills.slice(0, 4).map((skill, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">
                      {skill}
                    </span>
                  ))}
                  {resumeData.skills.length > 4 && (
                    <span className="text-xs text-gray-500">+{resumeData.skills.length - 4} more</span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Latest Experience */}
          {resumeData.experience && resumeData.experience.length > 0 && (
            <div className="flex items-start gap-2">
              <HiBriefcase className="text-green-600 flex-shrink-0 mt-0.5" />
              <div className="min-w-0">
                <p className="text-xs font-medium text-gray-700">Latest Role:</p>
                <p className="text-xs truncate">{resumeData.experience[0].title} at {resumeData.experience[0].company}</p>
              </div>
            </div>
          )}

          {/* Education */}
          {resumeData.education && resumeData.education.length > 0 && (
            <div className="flex items-start gap-2">
              <HiAcademicCap className="text-purple-600 flex-shrink-0 mt-0.5" />
              <div className="min-w-0">
                <p className="text-xs font-medium text-gray-700">Education:</p>
                <p className="text-xs truncate">{resumeData.education[0].degree} from {resumeData.education[0].institution}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Full view for detailed preview
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 max-h-96 overflow-y-auto">
      <div className="space-y-6">
        {/* Personal Information */}
        <section>
          <div className="flex items-center gap-2 mb-3">
            <HiUser className="text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-800">Personal Information</h3>
          </div>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <p><span className="font-medium">Name:</span> {resumeData.personal_info?.name || 'N/A'}</p>
            <p><span className="font-medium">Email:</span> {resumeData.personal_info?.email || 'N/A'}</p>
            <p><span className="font-medium">Phone:</span> {resumeData.personal_info?.phone || 'N/A'}</p>
            <p><span className="font-medium">Location:</span> {resumeData.personal_info?.location || 'N/A'}</p>
          </div>
        </section>

        {/* Skills */}
        {resumeData.skills && resumeData.skills.length > 0 && (
          <section>
            <div className="flex items-center gap-2 mb-3">
              <HiLightBulb className="text-yellow-600" />
              <h3 className="text-lg font-semibold text-gray-800">Skills</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {resumeData.skills.map((skill, index) => (
                <span key={index} className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                  {skill}
                </span>
              ))}
            </div>
          </section>
        )}

        {/* Experience */}
        {resumeData.experience && resumeData.experience.length > 0 && (
          <section>
            <div className="flex items-center gap-2 mb-3">
              <HiBriefcase className="text-green-600" />
              <h3 className="text-lg font-semibold text-gray-800">Experience</h3>
            </div>
            <div className="space-y-4">
              {resumeData.experience.slice(0, 2).map((exp, index) => (
                <div key={index} className="border-l-4 border-gray-200 pl-4">
                  <p className="font-medium text-sm">{exp.title}</p>
                  <p className="text-sm text-gray-600">{exp.company}</p>
                  <p className="text-xs text-gray-500">{exp.duration}</p>
                </div>
              ))}
              {resumeData.experience.length > 2 && (
                <p className="text-xs text-gray-500">+{resumeData.experience.length - 2} more positions</p>
              )}
            </div>
          </section>
        )}

        {/* Education */}
        {resumeData.education && resumeData.education.length > 0 && (
          <section>
            <div className="flex items-center gap-2 mb-3">
              <HiAcademicCap className="text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-800">Education</h3>
            </div>
            <div className="space-y-3">
              {resumeData.education.slice(0, 2).map((edu, index) => (
                <div key={index} className="border-l-4 border-gray-200 pl-4">
                  <p className="font-medium text-sm">{edu.degree}</p>
                  <p className="text-sm text-gray-600">{edu.institution}</p>
                  <p className="text-xs text-gray-500">{edu.year}</p>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Certifications */}
        {resumeData.certifications && resumeData.certifications.length > 0 && (
          <section>
            <div className="flex items-center gap-2 mb-3">
              <HiBadgeCheck className="text-orange-600" />
              <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {resumeData.certifications.map((cert, index) => (
                <span key={index} className="bg-orange-100 text-orange-800 text-sm px-3 py-1 rounded-full">
                  {cert}
                </span>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default ResumePreview;
