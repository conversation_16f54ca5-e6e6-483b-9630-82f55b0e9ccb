import React, { useState } from "react";
import { FcGoogle } from "react-icons/fc";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import Cookies from "js-cookie";
import Loader from "components/Loader";

import LogoImage from 'assets/img/Visume-logo-icon.png';

export default function AdminSignin() {
  
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const loginData = { email, password };
  console.log("loginadmin:",loginData)

  const saveAdminLoginDataToCookie = (loginresData) => {
    // Clear existing cookies
    const allCookies = Cookies.get();
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName);
    }
    localStorage.clear();

    // Set admin-specific cookies
    Cookies.set("adminId", loginresData.admin_id, { expires: 7 });
    Cookies.set("jstoken", loginresData.token, { expires: 7 });
    Cookies.set("role", "admin", { expires: 7 });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (loginData.email && loginData.password) {
      setIsLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/admin/login`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(loginData),
          }
        );
        //   console.log(response)
        // const responseData = await response.json();
        // console.log(responseData)

        if (!response.ok) {
          toast.error(responseData.message);
        } else {
          saveAdminLoginDataToCookie(responseData);
          navigate("/admin/dashboard");
        }
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "An unexpected error occurred.";
        toast.error(errorMessage);
        console.error("Error:", error);
      }
      setIsLoading(false);
    } else {
      toast.error("Please enter email and password");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      {isLoading && <Loader text="Signing in..." />}
      
      <div className="w-full max-w-md bg-white shadow-2xl rounded-2xl p-8 space-y-6">
        {/* Visume Logo and Title */}
        <div className="text-center">
          <div className="py-5 flex items-center justify-center">
            <div className="font-poppins text-[24px] font-bold text-brand-500 dark:text-white text-left flex items-center">
              <img src={LogoImage} alt="Visume logo" className="h-7 w-7 mb-1" />
              <span className="ml-2 text-2xl font-bold">Visume.ai</span>
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">
            Admin Sign In
          </h2>
          <p className="text-gray-600">
            Access your Admin Control Panel
          </p>
        </div>

        {/* Google Sign In */}
        <div 
          className="flex items-center justify-center space-x-3 bg-gray-100 transition rounded-lg p-3 opacity-50 cursor-not-allowed"
          title="Google Sign In is currently unavailable"
        >
          <FcGoogle className="text-2xl" />
          <span className="font-semibold text-gray-700">
            Sign In with Google (disabled)
          </span>
        </div>

        {/* Divider */}
        <div className="flex items-center space-x-4">
          <div className="flex-grow h-px bg-gray-300"></div>
          <span className="text-gray-500 text-sm">or</span>
          <div className="flex-grow h-px bg-gray-300"></div>
        </div>

        {/* Sign In Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Email
            </label>
            <input
              type="email"
              id="email"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500"
              placeholder="Enter your admin email"
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Password
            </label>
            <input
              type="password"
              id="password"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500"
              placeholder="Enter your admin password"
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          <div className="flex justify-end items-center">
            <a href="/admin/forgot-password" className="text-sm text-brand-600 hover:underline">
              Forgot Password?
            </a>
          </div>

          <button
            type="submit"
            className="w-full bg-brand-600 text-white py-3 rounded-lg hover:bg-brand-700 transition font-semibold"
          >
            Admin Sign In
          </button>
        </form>
      </div>
    </div>
  );
}