import React, { useEffect, useState } from "react";
import { FcGoogle } from "react-icons/fc";
import { googleOAuthPopup } from "../../utils/googleAuth";
import Checkbox from "components/checkbox";
import Cookies from "js-cookie";
import Loader from "components/Loader";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import FixedPlugin from "components/fixedPlugin/FixedPlugin";
import { Code, Play } from "lucide-react";

export default function EmpSignIn() {
  // State to store email and password
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const GOOGLE_CLIENT_ID = "YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com";

  const loginData = {
    email: email,
    password: password,
  };
  const saveloginresDataToCookie = (loginresData) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    if (loginresData.role === "jobseeker") {
      // store only 'candId'
      Cookies.set("candId", loginresData.cand_id, { expires: 7 }); // Expires in 7 days
    } else {
      // store only 'candId'
      Cookies.set("empId", loginresData.emp_id, { expires: 7 }); // Expires in 7 days
      // store only 'candId'
      Cookies.set("employerId", loginresData.employerId, { expires: 7 }); // Expires in 7 days
    }
    // store only 'token'
    Cookies.set("jstoken", loginresData.token, { expires: 7 }); // Expires in 7 days
    // store only 'role'
    Cookies.set("role", loginresData.role, { expires: 7 }); // Expires in 7 days
  };

  // Google Sign-In handler
  const handleGoogleSignIn = () => {
    googleOAuthPopup(async ({ accessToken, idToken }) => {
      let googleUser = {};
      try {
        // Optionally fetch user info from Google
        const userInfoRes = await fetch(
          "https://www.googleapis.com/oauth2/v3/userinfo",
          {
            headers: { Authorization: `Bearer ${accessToken}` },
          }
        );
        const userInfo = await userInfoRes.json();
        googleUser = {
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
        };

        // Send idToken to backend for verification and login
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/login-google`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ idToken }),
          }
        );
        const responseData = await response.json();
        if (!response.ok) {
          const msg = responseData.message
            ? responseData.message.toLowerCase()
            : "";
          if (
            msg.includes("not an employer account") ||
            msg.includes("user not found") ||
            msg.includes("register first") ||
            msg.includes("employer profile not found")
          ) {
            toast.error("Account not registered. Redirecting to registration...");
            setTimeout(() => {
              navigate("/create-account/employer", {
                state: {
                  email: googleUser.email || "",
                  name: googleUser.name || "",
                  picture: googleUser.picture || ""
                }
              });
            }, 1500);
            return;
          }
          toast.error(responseData.message || "Google login failed");
          return;
        }
        toast.success(
          googleUser.email
            ? `Signed in as ${googleUser.email}`
            : `Google login successful`
        );
        saveloginresDataToCookie(responseData);
        if (
          responseData.role &&
          responseData.role.toLowerCase() === "employer"
        ) {
          window.location.href = "/employer/dashboard";
        } else if (
          responseData.role &&
          responseData.role.toLowerCase() === "jobseeker"
        ) {
          window.location.href = "/candidate/dashboard";
        } else {
          window.location.href = "/";
        }
      } catch (err) {
        toast.error(err.message || "Google Sign-In failed");
      }
    });
  };

  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault(); // Prevent default form submission behavior

    // Log the loginData to ensure it's being updated
    console.log("Login Data:", loginData);
    if (loginData.email && loginData.password) {
      setIsLoading(true);
      try {
        // Replace the URL with your actual endpoint
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/login-employer`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(loginData), // Send JSON
          }
        );

        // Log the response
        const responseData = await response.json();
        if (!response.ok) {
          toast.error(responseData.message);
        } else {
          console.log("Response Data:", responseData);
          saveloginresDataToCookie(responseData);
          if (responseData.role.toLowerCase() === "jobseeker") {
            window.location.href = "/candidate/dashboard";
          } else {
            window.location.href = "/employer/dashboard";
          }
        }

        // Handle the response as needed
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || "An unexpected error occurred.";
        toast.error(errorMessage); // Display the JSON error message
        console.error("Error:", error);
      }
      setIsLoading(false);
    } else {
      toast.error("enter email and password");
    }
  };

  return (
    <div>
      <div className="relative float-right h-full min-h-screen w-full  !bg-white dark:!bg-navy-900">
        <FixedPlugin />
        <main className={`mx-auto min-h-screen `}>
          <div className="relative flex">
            <div className="mx-auto flex min-h-full w-full flex-col justify-start pt-12 md:max-w-[75%]  lg:max-w-[1013px] lg:px-8 lg:pt-0 xl:min-h-[100vh] xl:max-w-[1383px] xl:px-0 xl:pl-[70px]">
              <div className=" mb-auto flex h-[80%] flex-col items-start justify-start pl-5 pr-5 md:pl-12 md:pr-0 lg:max-w-[48%] lg:pl-0 xl:max-w-full">
                <div className="mb-16 mt-5 flex h-full w-full items-center justify-center px-2 md:mx-0 md:px-0 lg:mb-10 lg:items-center lg:justify-start">
                  {/* Loader */}
                  {isLoading && <Loader text={"Signing in..."} />}

                  {/* Sign in section */}
                  <div className=" w-full max-w-full md:mt-[5rem] flex-col items-center md:pl-4 lg:pl-0 xl:max-w-[420px]">
                    <h4 className="mb-2.5 text-4xl font-bold text-navy-700 dark:text-white">
                      Employer Sign In
                    </h4>
                    <p className="mb-3 ml-1 text-base text-gray-600">
                      Enter your email and password to sign in!
                    </p>
                    <button
                      type="button"
                      className="mb-3 flex h-[50px] w-full cursor-pointer items-center justify-center gap-2 rounded-xl bg-lightPrimary transition hover:opacity-90 dark:bg-navy-800"
                      onClick={handleGoogleSignIn}
                      title="Sign in with Google"
                    >
                      <div className="rounded-full text-xl">
                        <FcGoogle />
                      </div>
                      <h5 className="text-sm font-medium text-navy-700 dark:text-white">
                        Sign In with Google
                      </h5>
                    </button>
                    <div className="mb-6 flex items-center  gap-3">
                      <div className="h-px w-full bg-gray-200 dark:bg-navy-700" />
                      <p className="text-base text-gray-600 dark:text-white">
                        {" "}
                        or{" "}
                      </p>
                      <div className="h-px w-full bg-gray-200 dark:bg-navy-700" />
                    </div>

                    {/* Sign In Form */}
                    <form
                      onSubmit={handleSubmit}
                      onKeyDown={(e) => e.key === "Enter" && handleSubmit(e)}
                    >
                      <div className="mb-4">
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Email Address
                        </label>
                        <input
                          type="email"
                          id="email"
                          className="mt-2 block w-full rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter your email"
                          onChange={(event) => setEmail(event.target.value)}
                        />
                      </div>

                      {/* Password Field */}
                      <div className="mb-6">
                        <label
                          htmlFor="password"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Password
                        </label>
                        <input
                          type="password"
                          id="password"
                          className="mt-2 block w-full rounded-md border border-gray-300 px-4 py-2 text-sm outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter your password"
                          onChange={(event) => setPassword(event.target.value)}
                        />
                      </div>

                      {/* Checkbox */}
                      <div className="mb-4 flex items-center justify-between px-2">
                        <div className="flex items-center">
                          <Checkbox />
                          <p className="ml-2 text-sm font-medium text-navy-700 dark:text-white">
                            Keep me logged In
                          </p>
                        </div>
                        <a
                          className="text-sm font-medium text-brand-500 hover:text-brand-600 dark:text-white"
                          href=" "
                        >
                          Forgot Password?
                        </a>
                      </div>

                      <button
                        type="submit"
                        className="linear mt-2 w-full rounded-xl bg-brand-500 py-[12px] text-base font-medium text-white transition duration-200 hover:bg-brand-600 active:bg-brand-700 dark:bg-brand-400 dark:text-white dark:hover:bg-brand-300 dark:active:bg-brand-200"
                      >
                        Sign In
                      </button>
                    </form>

                    <div className="mt-4 flex flex-col items-start space-y-2">
                      <button
                      onClick={()=>navigate('/candidate/signin')}
                        className="linear mt-2 w-full rounded-xl bg-gray-100 py-[12px] text-base font-medium text-black transition duration-200 hover:bg-gray-400 border border-[1px solid]"
                      >
                         Sign In as JobSeeker.
                      </button>
                      <span className="text-sm font-medium text-navy-700 dark:text-gray-600">
                        Not registered yet?
                      </span>
                      <div className="flex space-x-4">
                        <a
                          href="/create-account/candidate"
                          className="rounded-md border border-brand-500 px-4 py-2 text-sm font-medium text-brand-500 transition hover:bg-brand-50 hover:text-brand-600 dark:text-white dark:hover:bg-gray-700"
                        >
                          Register as Jobseeker
                        </a>
                        <a
                          href="/create-account/employer"
                          className="rounded-md border border-brand-500 px-4 py-2 text-sm font-medium text-brand-500 transition hover:bg-brand-50 hover:text-brand-600 dark:text-white dark:hover:bg-gray-700"
                        >
                          Register as Employer
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="absolute right-0 top-0 mr-5 mt-10 hidden h-[90vh] w-full overflow-hidden rounded-lg bg-gradient-to-br from-brand-500 to-brand-600 p-8 text-white dark:bg-brand-700 md:hidden md:w-1/2 lg:block lg:max-w-2xl">
                  <div className="relative h-full">
                    <div className="mb-8 flex items-center">
                      <Code className="h-8 w-8" />
                      <span className="ml-2 text-xl font-bold">Visume.ai</span>
                    </div>
                    <div className="mb-12">
                      <h2 className="mb-4 text-5xl font-extrabold leading-tight">
                        <span className="font-light"> Discover top</span>
                        <br />
                        Talent Effortlessly
                        <br />
                        <span className="font-light italic"> in minutes.</span>
                      </h2>
                      <p className="text-xl text-white/80">
                        Access a pool of qualified candidates with personalized
                        video profiles.
                        <br />
                        <span className="font-bold">Streamline hiring</span> and
                        find the perfect fit faster!
                      </p>
                    </div>
                    <button className="group flex items-center rounded-full bg-white px-6 py-3 text-lg font-semibold text-brand-600 shadow-lg transition-all hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-brand-600">
                      <Play className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
                      Watch demo
                    </button>
                    <div className="absolute bottom-0 right-0 -mb-16 -mr-16 h-64 w-64 rounded-full bg-white/20 blur-3xl" />
                    <div className="absolute left-0 top-1/2 -ml-16 -mt-16 h-32 w-32 rounded-full bg-white/20 blur-2xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
