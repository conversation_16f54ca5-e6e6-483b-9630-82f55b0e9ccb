import React from "react";
import {
  Briefcase,
  ChartBar,
  Trophy,
  Upload,
  Users,
  X,
  Check,
  FileText,
} from "lucide-react";

const CreateAccountForm = ({
  isProcessing,
  navigate,
  LogoImage,
  videoRes,
  profilePicPreview,
  imageError,
  setImageError,
  profilePic,
  profilePicError,
  errors,
  handleProfilePicChange,
  profilePicInputRef,
  handleRemoveProfilePic,
  formData,
  setFormData,
  handleInputChange,
  emailValidation,
  selectedLanguages,
  isLanguageOpen,
  setIsLanguageOpen,
  languageOptions,
  languageDropdownRef,
  toggleLanguage,
  selectedLocations,
  isLocationOpen,
  setIsLocationOpen,
  locationOptions,
  locationDropdownRef,
  toggleLocation,
  handlePhoneNumberChange,
  showSendOTPButton,
  otpVerified,
  handleSendOTPClick,
  otp,
  otpError,
  handleOTPChange,
  handleVerifyOTP,
  handleFileUpload,
  uploadedFile,
  showOTPModal,
  setShowOTPModal,
  handleSubmit,
}) => (
  <div className="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-brand-50 px-2 py-8 font-sans">
    {/* Loading Overlay */}
    {isProcessing && (
      <div className="bg-black/40 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md">
        <div className="w-full max-w-md rounded-2xl border border-brand-100 bg-white p-10 text-center shadow-2xl">
          <div className="mb-4 flex justify-center">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-brand-600"></div>
          </div>
          <h3 className="mb-2 text-xl font-bold text-brand-700">
            Processing your resume...
          </h3>
          <p className="text-sm text-gray-500">
            Please wait while we create your profile.
          </p>
        </div>
      </div>
    )}
    <div className="mx-auto max-w-5xl">
      <div className="mb-8 flex items-center gap-2">
        <div className="flex items-center text-left font-poppins text-[26px] font-extrabold text-brand-700 dark:text-white">
          <img src={LogoImage} alt="Visume logo" className="mb-1 h-8 w-8" />
          <span className="ml-2 text-2xl font-extrabold tracking-tight">
            Visume.ai
          </span>
        </div>
        <div className="ml-auto text-sm text-gray-600">
          Already Registered?{" "}
          <button
            onClick={() => navigate("/candidate/signIn")}
            className="cursor-pointer text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
          >
            Login here
          </button>
        </div>
      </div>

      <div className="grid gap-10 md:grid-cols-[2fr,1fr]">
        <div className="rounded-2xl border border-brand-100 bg-white p-8 shadow-lg">
          <h2 className="mb-2 text-3xl font-extrabold tracking-tight text-brand-700">
            Create your Visume profile
          </h2>
          <p className="mb-6 text-base text-gray-500">
            Create & share video resumes on India's No.1 AI Video Resume
            Platform
          </p>
          <hr className="mb-6 border-t border-brand-100" />

          <form
            className="space-y-6"
            onSubmit={handleSubmit}
            autoComplete="off"
          >
            {/* Profile Picture Upload */}
            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Profile Picture{" "}
                <span className="text-gray-400">(optional)</span>
              </label>
              <input
                type="file"
                accept="image/jpeg, image/png, image/jpg"
                className="hidden"
                id="profile-pic-upload-input"
                onChange={handleProfilePicChange}
                ref={profilePicInputRef}
              />
              <div
                className="cursor-pointer rounded-xl border-2 border-dashed bg-gray-50 p-4 text-center transition hover:border-brand-400"
                onClick={() =>
                  document.getElementById("profile-pic-upload-input").click()
                }
              >
                {profilePicPreview && !imageError ? (
                  <div className="flex flex-col items-center">
                    <div className="relative">
                      <img
                        src={
                          typeof profilePicPreview === "string"
                            ? profilePicPreview
                            : URL.createObjectURL(profilePicPreview)
                        }
                        alt="Profile Preview"
                        className="mx-auto mb-2 h-24 w-24 rounded-full border-2 border-brand-300 object-cover shadow-lg"
                        onError={() => setImageError(true)}
                      />
                      <button
                        type="button"
                        aria-label="Remove profile picture"
                        className="absolute right-0 top-0 rounded-full border bg-white p-1 shadow transition hover:bg-brand-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveProfilePic();
                          setImageError(false);
                        }}
                      >
                        <X className="h-5 w-5 text-gray-500" />
                      </button>
                    </div>
                    <span className="mt-1 text-xs text-gray-500">
                      {profilePic?.name}
                    </span>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <div className="relative">
                      {/* Inline SVG shadow avatar as default */}
                      <svg
                        viewBox="0 0 96 96"
                        width="96"
                        height="96"
                        className="mx-auto mb-2 h-24 w-24 rounded-full border-2 border-brand-100 object-cover"
                        aria-label="Default Profile"
                      >
                        <circle cx="48" cy="48" r="48" fill="#e5e7eb" />
                        <ellipse
                          cx="48"
                          cy="40"
                          rx="20"
                          ry="20"
                          fill="#cbd5e1"
                        />
                        <ellipse
                          cx="48"
                          cy="78"
                          rx="28"
                          ry="14"
                          fill="#d1d5db"
                        />
                      </svg>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Upload a profile picture (JPG, JPEG, PNG, Max 5MB)
                    </p>
                  </div>
                )}
              </div>
              {(profilePicError || errors.profilePic) && (
                <p className="mt-2 text-xs text-red-500">
                  {profilePicError || errors.profilePic}
                </p>
              )}
            </div>
            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Full name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="fullName"
                placeholder="Your full name"
                className="w-full rounded-lg border-2 border-brand-100 p-3 text-gray-700 shadow-sm transition focus:border-brand-500 focus:outline-none focus:ring-2 focus:ring-brand-500"
                value={formData.fullName}
                onChange={handleInputChange}
                required
              />
              {errors.fullName && (
                <p className="mt-2 text-xs text-red-500">{errors.fullName}</p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Email ID <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                placeholder="Your email address"
                className={`w-full rounded-lg border-2 p-3 text-gray-700 shadow-sm transition focus:border-brand-500 focus:outline-none focus:ring-2 ${
                  emailValidation.isChecking
                    ? "border-blue-300 focus:ring-blue-500"
                    : emailValidation.isValid === null
                    ? "border-brand-100 focus:ring-brand-500"
                    : emailValidation.isValid
                    ? "border-green-500 focus:ring-green-500"
                    : "border-red-500 focus:ring-red-500"
                }`}
                value={formData.email}
                onChange={handleInputChange}
                required
              />
              {/* Email validation feedback */}
              {emailValidation.message && (
                <p
                  className={`mt-2 text-xs ${
                    emailValidation.isChecking
                      ? "text-blue-600"
                      : emailValidation.isValid
                      ? "text-green-600"
                      : "text-red-500"
                  }`}
                >
                  {emailValidation.isChecking && (
                    <span className="inline-flex items-center">
                      <svg
                        className="-ml-1 mr-2 h-3 w-3 animate-spin text-blue-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    </span>
                  )}
                  {emailValidation.message}
                </p>
              )}
              {errors.email && (
                <p className="mt-2 text-xs text-red-500">{errors.email}</p>
              )}
              <p className="mt-2 text-xs text-gray-400">
                Employers will contact you on this email
              </p>
            </div>

            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Password <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                name="password"
                placeholder="Minimum 6 characters"
                className="w-full rounded-lg border-2 border-brand-100 p-3 text-gray-700 shadow-sm transition focus:border-brand-500 focus:outline-none focus:ring-2 focus:ring-brand-500"
                value={formData.password}
                onChange={handleInputChange}
                required
                minLength={6}
              />
              {errors.password && (
                <p className="mt-2 text-xs text-red-500">{errors.password}</p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Mobile number <span className="text-red-500">*</span>
              </label>
              <div className="flex">
                <span className="inline-flex items-center rounded-l-lg border-2 border-brand-100 bg-gray-50 px-3 text-gray-500">
                  +91
                </span>
                <input
                  type="tel"
                  name="mobile"
                  maxLength="10"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  placeholder="Your mobile number"
                  className="w-full rounded-r-lg border-2 border-brand-100 p-3 text-gray-700 shadow-sm transition focus:border-brand-500 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  value={formData.mobile}
                  onChange={handlePhoneNumberChange}
                  required
                />
              </div>
              {errors.mobile && (
                <p className="mt-2 text-xs text-red-500">{errors.mobile}</p>
              )}
              <p className="mt-2 text-xs text-gray-400">
                Employers will contact you on this number
              </p>
              {showSendOTPButton && !otpVerified && (
                <button
                  onClick={handleSendOTPClick}
                  className="mt-2 rounded-lg bg-brand-600 px-4 py-2 font-semibold text-white shadow transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500"
                >
                  Send OTP
                </button>
              )}
              {otpVerified && (
                <p className="mt-2 text-xs text-green-600">
                  Phone number verified!
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Gender <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-4">
                {["male", "female", "other"].map((gender) => (
                  <button
                    key={gender}
                    type="button"
                    className={`flex items-center gap-2 rounded-lg border-2 px-4 py-2 transition-all duration-150
                        ${
                          formData.gender === gender
                            ? "border-brand-500 bg-brand-50 shadow-lg"
                            : "border-brand-100 bg-white hover:bg-brand-50"
                        }`}
                    onClick={() => {
                      setFormData({ ...formData, gender });
                      if (typeof setErrors === "function")
                        setErrors({ ...errors, gender: "" });
                    }}
                  >
                    <span
                      className={`flex h-6 w-6 items-center justify-center rounded-full border-2
                          ${
                            gender === "male"
                              ? "border-blue-500"
                              : gender === "female"
                              ? "border-pink-500"
                              : "border-yellow-500"
                          }
                          ${formData.gender === gender ? "bg-gray-200" : ""}
                        `}
                    >
                      {gender === "male" && <span>♂️</span>}
                      {gender === "female" && <span>♀️</span>}
                      {gender === "other" && <span>⚧️</span>}
                    </span>
                    <span className="text-sm font-medium capitalize">
                      {gender}
                    </span>
                  </button>
                ))}
              </div>
              {errors.gender && (
                <p className="mt-2 text-xs text-red-500">{errors.gender}</p>
              )}
            </div>

            <div className="flex gap-4">
              <div className="w-full" ref={languageDropdownRef}>
                <label
                  htmlFor="language-select"
                  className="mb-2 block text-sm font-semibold text-gray-700"
                >
                  Languages known <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    id="language-select"
                    className="flex w-full items-center justify-between rounded-lg border-2 border-brand-100 bg-white px-4 py-2 text-left shadow-lg transition focus:outline-none focus:ring-2 focus:ring-brand-500"
                    onClick={() => setIsLanguageOpen(!isLanguageOpen)}
                    aria-haspopup="listbox"
                    aria-expanded={isLanguageOpen}
                  >
                    <span>
                      {selectedLanguages.length > 0
                        ? `${selectedLanguages.length} selected`
                        : "Select languages"}
                    </span>
                    <span className="pointer-events-none flex items-center">
                      <svg
                        className="h-5 w-5 text-brand-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  {isLanguageOpen && (
                    <ul
                      className="absolute z-10 mt-2 max-h-60 w-full overflow-auto rounded-lg border border-brand-100 bg-white py-1 text-base shadow-lg ring-1 ring-brand-200 ring-opacity-10 focus:outline-none sm:text-sm"
                      role="listbox"
                      aria-labelledby="language-select"
                      tabIndex={-1}
                    >
                      {languageOptions.map((language) => (
                        <li
                          key={language}
                          className={`${
                            selectedLanguages.includes(language)
                              ? "bg-brand-100 text-brand-900"
                              : "text-gray-900"
                          } relative cursor-pointer select-none py-2 pl-3 pr-9 transition hover:bg-brand-50`}
                          role="option"
                          aria-selected={selectedLanguages.includes(language)}
                          onClick={() => toggleLanguage(language)}
                        >
                          <span
                            className={`block truncate ${
                              selectedLanguages.includes(language)
                                ? "font-semibold"
                                : "font-normal"
                            }`}
                          >
                            {language}
                          </span>
                          {selectedLanguages.includes(language) && (
                            <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-brand-600">
                              <Check className="h-5 w-5" aria-hidden="true" />
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {selectedLanguages.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedLanguages.map((lang) => (
                      <span
                        key={lang}
                        className="inline-flex items-center rounded-full bg-brand-100 px-2 py-1 text-xs font-medium text-brand-800"
                      >
                        {lang}
                        <button
                          type="button"
                          className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-brand-400 hover:bg-brand-200 hover:text-brand-500 focus:bg-brand-500 focus:text-white focus:outline-none"
                          onClick={() => toggleLanguage(lang)}
                        >
                          <span className="sr-only">Remove {lang}</span>
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                {errors.languages && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.languages}
                  </p>
                )}
              </div>

              <div className="w-full" ref={locationDropdownRef}>
                <label
                  htmlFor="location-select"
                  className="mb-2 block text-sm font-semibold text-gray-700"
                >
                  Preferred Locations <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    id="location-select"
                    className="flex w-full items-center justify-between rounded-lg border-2 border-brand-100 bg-white px-4 py-2 text-left shadow-lg transition focus:outline-none focus:ring-2 focus:ring-brand-500"
                    onClick={() => setIsLocationOpen(!isLocationOpen)}
                    aria-haspopup="listbox"
                    aria-expanded={isLocationOpen}
                  >
                    <span>
                      {selectedLocations.length > 0
                        ? `${selectedLocations.length} selected`
                        : "Select locations"}
                    </span>
                    <span className="pointer-events-none flex items-center">
                      <svg
                        className="h-5 w-5 text-brand-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  {isLocationOpen && (
                    <ul
                      className="absolute z-10 mt-2 max-h-60 w-full overflow-auto rounded-lg border border-brand-100 bg-white py-1 text-base shadow-lg ring-1 ring-brand-200 ring-opacity-10 focus:outline-none sm:text-sm"
                      role="listbox"
                      aria-labelledby="location-select"
                      tabIndex={-1}
                    >
                      {locationOptions.map((location) => (
                        <li
                          key={location}
                          className={`${
                            selectedLocations.includes(location)
                              ? "bg-brand-100 text-brand-900"
                              : "text-gray-900"
                          } relative cursor-pointer select-none py-2 pl-3 pr-9 transition hover:bg-brand-50`}
                          role="option"
                          aria-selected={selectedLocations.includes(location)}
                          onClick={() => toggleLocation(location)}
                        >
                          <span
                            className={`block truncate ${
                              selectedLocations.includes(location)
                                ? "font-semibold"
                                : "font-normal"
                            }`}
                          >
                            {location}
                          </span>
                          {selectedLocations.includes(location) && (
                            <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-brand-600">
                              <Check className="h-5 w-5" aria-hidden="true" />
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {selectedLocations.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedLocations.map((loc) => (
                      <span
                        key={loc}
                        className="inline-flex items-center rounded-full bg-brand-100 px-2 py-1 text-xs font-medium text-brand-800"
                      >
                        {loc}
                        <button
                          type="button"
                          className="ml-1 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-brand-400 hover:bg-brand-200 hover:text-brand-500 focus:bg-brand-500 focus:text-white focus:outline-none"
                          onClick={() => toggleLocation(loc)}
                        >
                          <span className="sr-only">Remove {loc}</span>
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                {errors.locations && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.locations}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-semibold text-gray-700">
                Resume Upload <span className="text-red-500">*</span>
              </label>
              <input
                type="file"
                accept=".pdf, .doc, .docx"
                className="hidden"
                onChange={handleFileUpload}
                id="resume-upload-input"
              />
              <div
                className="cursor-pointer rounded-xl border-2 border-dashed bg-gray-50 p-4 text-center transition hover:border-brand-400"
                onClick={() =>
                  document.getElementById("resume-upload-input").click()
                }
              >
                {uploadedFile ? (
                  <div className="flex items-center justify-center gap-2">
                    <FileText className="h-10 w-10 text-brand-400" />
                    <span className="ml-2 text-xs font-medium text-gray-700">
                      {uploadedFile.name}
                    </span>
                  </div>
                ) : (
                  <>
                    <Upload className="mx-auto h-10 w-10 text-brand-200" />
                    <p className="mt-1 text-xs text-gray-500">
                      Upload your resume (PDF, DOC, DOCX, Max 5MB)
                    </p>
                  </>
                )}
              </div>
              {errors.resume && (
                <p className="mt-2 text-xs text-red-500">{errors.resume}</p>
              )}
            </div>

            <div className="mt-4 flex items-center gap-2">
              <input
                type="checkbox"
                className="rounded-lg border-2 border-brand-100 text-brand-600 focus:ring-brand-500"
              />
              <span className="text-xs text-gray-500">
                Send me referrals & job opportunities via SMS, email, and
                WhatsApp
              </span>
            </div>

            <div className="mt-4 text-xs text-gray-400">
              By clicking{" "}
              <span className="font-semibold text-brand-600">Register</span>,
              you agree to the{" "}
              <a
                href="#"
                className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
              >
                Terms and Conditions
              </a>{" "}
              &{" "}
              <a
                href="#"
                className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
              >
                Privacy Policy
              </a>
            </div>

            <button
              type="submit"
              className="mt-6 w-full rounded-xl bg-brand-600 py-3 text-lg font-semibold text-white shadow-lg transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500"
            >
              Create Visume Profile
            </button>
          </form>
        </div>

        <div className="h-max rounded-2xl border border-brand-100 bg-white p-8 shadow-lg">
          <div className="mb-8">
            <div className="mb-4 flex justify-center">
              <img
                src={videoRes}
                alt="Visume illustration"
                className="w-64 rounded-xl shadow"
              />
            </div>
            <h3 className="mb-6 text-center text-xl font-bold text-brand-700">
              Create a Visume, get
            </h3>
            <ul className="space-y-5">
              <li className="flex items-center gap-3">
                <div className="flex items-center justify-center rounded-full bg-blue-100 p-2 shadow">
                  <ChartBar className="h-5 w-5 text-blue-600" />
                </div>
                <span className="text-sm text-gray-700">
                  <strong className="font-semibold">50% higher</strong> chance
                  of getting hired compared to traditional resumes
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="flex items-center justify-center rounded-full bg-green-100 p-2 shadow">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <span className="text-sm text-gray-700">
                  Direct profile forwarding to{" "}
                  <strong className="font-semibold">500+ top recruiters</strong>{" "}
                  in your industry
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="flex items-center justify-center rounded-full bg-purple-100 p-2 shadow">
                  <Trophy className="h-5 w-5 text-purple-600" />
                </div>
                <span className="text-sm text-gray-700">
                  Stand out with{" "}
                  <strong className="font-semibold">
                    professional video presentations
                  </strong>{" "}
                  showcasing your skills
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="flex items-center justify-center rounded-full bg-orange-100 p-2 shadow">
                  <Briefcase className="h-5 w-5 text-orange-600" />
                </div>
                <span className="text-sm text-gray-700">
                  Access to{" "}
                  <strong className="font-semibold">
                    premium job listings
                  </strong>{" "}
                  from Fortune 500 companies
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    {showOTPModal && (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md">
        <div className="bg-black absolute inset-0 opacity-40"></div>
        <div className="relative z-50 w-full max-w-md rounded-2xl border border-brand-100 bg-white p-8 shadow-2xl">
          <button
            onClick={() => setShowOTPModal(false)}
            className="absolute right-4 top-4 text-gray-400 transition hover:text-brand-600"
          >
            <X className="h-6 w-6" />
          </button>
          <h3 className="mb-6 text-center text-xl font-bold text-brand-700">
            Enter OTP
          </h3>
          <div className="mb-6 flex justify-center gap-3">
            {otp.map((digit, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOTPChange(index, e.target.value)}
                className="h-12 w-12 rounded-lg border-2 border-brand-100 text-center text-xl font-bold shadow transition focus:border-brand-500 focus:ring-2 focus:ring-brand-500"
                aria-label={`OTP digit ${index + 1}`}
              />
            ))}
          </div>
          {otpError && (
            <p className="mb-4 text-center text-sm text-red-500">{otpError}</p>
          )}
          <div className="mt-2 flex justify-between">
            <button
              onClick={handleVerifyOTP}
              className="rounded-lg bg-brand-600 px-5 py-2 font-semibold text-white shadow transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
            >
              Verify OTP
            </button>
            <button
              onClick={handleSendOTPClick}
              className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800 focus:outline-none"
            >
              Resend OTP
            </button>
          </div>
        </div>
      </div>
    )}
  </div>
);

export default CreateAccountForm;
