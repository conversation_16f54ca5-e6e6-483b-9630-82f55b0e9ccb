import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { ArrowUpRight, EyeIcon, EyeOffIcon, HelpCircle } from "lucide-react";
import { HiAdjustments,HiLockClosed,HiOutlineUserGroup,} from "react-icons/hi";
import { MdCreditScore } from "react-icons/md";
import GeneralProfile from "./GeneralProfile";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import CandidateSettings from "views/candidate/candidateSettings";

const formatDate = (date) => {
  const dates = new Date(date);
  const options = { month: "long", day: "numeric", year: "numeric" };
  return dates.toLocaleDateString("en-US", options);
};

function ManageCredits() {

  const navigate = useNavigate();
  const location = useLocation();                                            //currentpath url 
  const [activeSection, setActiveSection] = useState("general");             
  const emp_id = Cookies.get("employerId");
  console.log(emp_id)
  const [empData, setEmpData] = useState({
    plan_name: "Visume Exclusive",
    creditsLeft: 100,
    totalCredits: 100,
    end_date: Date.now() + 365 * 24 * 60 * 60 * 1000,
  });

  useEffect(() => {
    // Extract the section from the URL
    const path = location.pathname.split("/").pop();       // console.log(path) ["employer"/"settings"].pop() output:settings

    if (path && path !== 'settings') {               
      setActiveSection(path);
    } else {
      setActiveSection('general');
    }
  }, [location]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setEmpData({
            plan_name: profileJson?.data?.plan_name || "Visume Exclusive",
            creditsLeft: profileJson?.data?.creditsLeft,
            totalCredits: profileJson?.data?.credits_assigned,
            end_date: profileJson?.data?.end_date,
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    fetchCandidates();
  }, [emp_id]);

  const handleSectionChange = (section) => {                      //section = general,password,team,plan-billing
    const path = section === 'general' ? '/employer/settings' : `/employer/settings/${section}`;
    navigate(path);
    setActiveSection(section);   
    console.log(activeSection)
  };

  const renderSection = () => {

    switch (activeSection) {
      case "general":
        return (<div className="h-full overflow-y-auto"><GeneralProfile /></div>);
      case "password":
        return (
         <CandidateSettings />
        );
      case "team":
        return (
          <div>
            <h2 className="mb-4 text-xl font-semibold">Team Management</h2>
            <p>Manage your team members and roles here.</p>
          </div>
        );
      case "plan-billing":
        return (
          <div>
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-lg font-semibold">Plan & Billing</h2>
              <div>
                <button className="flex items-center text-sm text-gray-600 hover:text-gray-800">
                  Manage payments
                  <ArrowUpRight className="ml-1 h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="mb-6 rounded-lg border border-gray-300 p-4">
              <div className="mb-2 flex items-center justify-between">
                <h3 className="text-sm font-medium">Current plan</h3>
                <button className="text-sm text-gray-800 hover:text-brand-300">
                  Change plan
                </button>
              </div>
              <div className="flex items-center">
                <span className="mr-2 text-2xl font-bold">
                  {empData.plan_name} Plan
                </span>
                <span
                  className={`ml-2 h-2 w-2 rounded-full ${
                    new Date(empData.end_date) > Date.now()
                      ? "bg-green-400"
                      : "bg-red-400"
                  }`}
                ></span>
                <span
                  className={`ml-1 text-sm ${
                    new Date(empData.end_date) > Date.now()
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {new Date(empData.end_date) > Date.now() ? "Active" : "Inactive"}
                </span>
              </div>
              <p className="mt-1 text-sm text-gray-600">
                Renew at {formatDate(empData.end_date)}
              </p>
            </div>

            <div className="mb-6">
              <h3 className="mb-2 text-sm font-medium">Usage</h3>
              <p className="mb-4 text-sm text-gray-600">
                Your usage is updated at the end of every day.
              </p>
              <div className="grid grid-cols-3 gap-4">
                <div className="rounded-lg border border-gray-300 p-4">
                  <div className="mb-1 flex items-center">
                    <span className="mr-1 text-sm font-medium">
                      Profile credits
                    </span>
                    <HelpCircle className="h-3 w-3 text-gray-400" />
                  </div>
                  <p className="mb-1 text-lg font-semibold">
                    {empData.creditsLeft} of {empData.totalCredits}
                  </p>
                  <div className="h-2 w-full rounded-full bg-gray-200">
                    <div
                      className="h-2 rounded-full bg-brand-400"
                      style={{
                        width: `${
                          ((empData.totalCredits - empData.creditsLeft) /
                            empData.totalCredits) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="rounded-lg border border-gray-300 p-4">
                  <div className="mb-1 flex items-center">
                    <p className="mr-1 text-sm font-medium">AI Interviews</p>
                    <HelpCircle className="h-3 w-3 text-gray-400" />
                  </div>
                  <p className="mb-1 text-lg font-semibold">0 of 3</p>
                  <div className="h-2 w-full rounded-full bg-gray-200">
                    <div
                      className="h-2 rounded-full bg-brand-400"
                      style={{ width: "0%" }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (<div className="h-full overflow-y-auto"><GeneralProfile /></div>);
    }
  };

  return (
    // <div className="min-h-[80vh] rounded-lg bg-white p-6 overflow-hidden shadow-sm">

    //   <div className="flex items-center justify-between -ml-4 md:-ml-0">
    //     <h1 className="mb-4 text-[12px]  tracking-wider md:tracking-tight md:text-2xl font-semibold fixed top-32">Settings</h1>
    //   </div>

    //   <div className="grid grid-cols-5 gap-4">
        
    //     <div className="col-span-1 border-r border-gray-300 pr-4 -ml-5 md:-ml-0">
          
    //       <h2 className="text-[11px] md:text-lg mb-4 font-medium text-gray-500 fixed top-44">My account</h2>
    //       <ul className="space-y-4 text-[10px] md:text-base tracking-normal md:tracking-tight">
    //         <li>
    //           <button
    //             onClick={() => handleSectionChange("general")}
    //             className={`flex items-center gap-1 md:gap-2 ${
    //               activeSection === "general"
    //                 ? "font-semibold text-gray-900"
    //                 : "text-gray-700"
    //             } hover:text-gray-900`}
    //           >
    //             <HiAdjustments />
    //             General
    //           </button>
    //         </li>
    //         <li>
    //           <button
    //             onClick={() => handleSectionChange("password")}
    //             className={`flex items-center gap-1 md:gap-2 ${
    //               activeSection === "password"
    //                 ? "font-semibold text-gray-900"
    //                 : "text-gray-700"
    //             } hover:text-gray-900`}
    //           >
    //             <HiLockClosed />
    //             Password
    //           </button>
    //         </li>
    //       </ul>
    //       <h2 className="mb-4 mt-6  text-[11px] md:text-lg font-medium text-gray-500 fixed top-72 ">
    //         Organization
    //       </h2>
    //       <ul className="space-y-4 text-[10px] md:text-base tracking-normal md:tracking-tight  fixed top-[370px]">
    //         <li>
    //           <button
    //             onClick={() => handleSectionChange("plan-billing")}
    //             className={` flex items-center gap-1 md:gap-2  ${
    //               activeSection === "plan-billing"
    //                 ? "font-semibold text-gray-900"
    //                 : "text-gray-700"
    //             } hover:text-gray-900`}
    //           >
    //             <MdCreditScore />
    //             Plan & Billing
    //           </button>
    //         </li>
    //         <li>
    //           <button
    //             onClick={() => handleSectionChange("team")}
    //             className={`text-md flex items-center gap-2 ${
    //               activeSection === "team"
    //                 ? "font-semibold text-gray-900"
    //                 : "text-gray-700"
    //             } hover:text-gray-900`}
    //           >
    //             <HiOutlineUserGroup />
    //             Team
    //           </button>
    //         </li>
    //       </ul>
    //     </div>

    //     <div className="col-span-4">{renderSection()}</div>
    //   </div>
    // </div>

     <div className="min-h-[80vh] rounded-lg bg-white fixed right-5 left-72 h-[100px] overflow-hidden shadow-sm ">


       
        <div className="-ml-4 md:-ml-0 ">
          <h1 className=" mb-4 text-[12px]  tracking-wider md:tracking-tight md:text-2xl font-semibold fixed top-32 pl-10">Settings</h1>
        </div>
    
        <div className="grid grid-cols-5 gap-4 pt-8  ">
          
          <div className="col-span-1 border-r border-gray-300  -ml-5 md:-ml-0 ">
            
            <h2 className="text-[11px] md:text-lg mb-4 font-medium text-gray-500 fixed top-44 pl-10">My account</h2>
    
            <ul className="space-y-4 text-[10px] md:text-base tracking-normal md:tracking-tight fixed top-56 pl-10">
    
              <li>
                <button
                  onClick={() => handleSectionChange("general")}
                  className={`flex items-center gap-1 md:gap-2 ${
                    activeSection === "general"
                      ? "font-semibold text-gray-900"
                      : "text-gray-700"
                  } hover:text-gray-900`}
                >
                  <HiAdjustments />
                  General
                </button>
              </li>
    
              <li>
                <button
                  onClick={() => handleSectionChange("password")}
                  className={`flex items-center gap-1 md:gap-2 ${
                    activeSection === "password"
                      ? "font-semibold text-gray-900"
                      : "text-gray-700"
                  } hover:text-gray-900`}
                >
                  <HiLockClosed />
                  Password
                </button>
              </li>
    
            </ul>
            <h2 className="mb-4 mt-6  text-[11px] md:text-lg font-medium text-gray-500 fixed top-72 pl-10">
              Organization
            </h2>
            <ul className="space-y-4 text-[10px] md:text-base tracking-normal md:tracking-tight fixed top-[370px] pl-10">
              <li>
                <button
                  onClick={() => handleSectionChange("plan-billing")}
                  className={` flex items-center gap-1 md:gap-2  ${
                    activeSection === "plan-billing"
                      ? "font-semibold text-gray-900"
                      : "text-gray-700"
                  } hover:text-gray-900`}
                >
                  <MdCreditScore />
                  Plan & Billing
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleSectionChange("team")}
                  className={`text-md flex items-center gap-2 ${
                    activeSection === "team"
                      ? "font-semibold text-gray-900"
                      : "text-gray-700"
                  } hover:text-gray-900`}
                >
                  <HiOutlineUserGroup />
                  Team
                </button>
              </li>
            </ul>
          </div>
    
          <div className="col-span-4 h-[calc(100vh-8rem)] overflow-y-auto">{renderSection()}</div>

        </div>
    
       </div>
  );
}

export default ManageCredits;