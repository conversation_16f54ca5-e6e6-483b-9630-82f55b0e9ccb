import React, { useState, useEffect } from "react";
import { 
  User, 
  Lock, 
  Bell, 
  Shield, 
  Building, 
  Upload,
  FileText,
  Camera,
  Check,
  EyeIcon,
  EyeOffIcon,
  Globe,
  Mail,
  Phone,
  MapPin
} from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const EmployerSettings = () => {
  const navigate = useNavigate();
  const empId = Cookies.get("empId");
  
  // Active tab state
  const [activeTab, setActiveTab] = useState("company");
  
  // Company profile state
  const [companyData, setCompanyData] = useState({
    companyName: "",
    companyUrl: "",
    description: "",
    industry: "",
    companySize: "",
    website: "",
    location: "",
    logo: null
  });
  
  // Personal profile state
  const [personalData, setPersonalData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    profileImage: null
  });
  
  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  
  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    candidateApplications: true,
    systemUpdates: true,
    marketingEmails: false,
    weeklyReports: true
  });
  
  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    companyVisibility: "public",
    showContactInfo: true,
    allowCandidateMessages: true,
    shareCompanyData: false
  });
  
  // Loading states
  const [loading, setLoading] = useState({
    company: false,
    personal: false,
    password: false,
    notifications: false,
    privacy: false
  });
  
  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });
  const [isEditingCompany, setIsEditingCompany] = useState(false);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});

  // Fetch employer data
  useEffect(() => {
    const fetchEmployerData = async () => {
      if (!empId) {
        toast.error("No Token Found, Please Login Again");
        navigate("/employer/signin");
        return;
      }

      try {
        // Simulate API call
        const mockData = {
          company: {
            companyName: "Tech Innovations Inc.",
            companyUrl: "tech-innovations",
            description: "Leading technology solutions provider",
            industry: "Technology",
            companySize: "51-200",
            website: "https://techinnovations.com",
            location: "San Francisco, CA",
            logo: null
          },
          personal: {
            name: "John Smith",
            email: "<EMAIL>",
            phone: "+****************",
            position: "HR Manager",
            department: "Human Resources",
            profileImage: null
          }
        };
        
        setCompanyData(mockData.company);
        setPersonalData(mockData.personal);
      } catch (error) {
        console.error("Failed to fetch employer data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchEmployerData();
  }, [empId, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading(prev => ({ ...prev, password: true }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Password updated successfully!");
    } catch (error) {
      toast.error("An unexpected error occurred.");
    } finally {
      setLoading(prev => ({ ...prev, password: false }));
    }
  };

  // Handle company update
  const handleCompanyUpdate = async () => {
    setLoading(prev => ({ ...prev, company: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Company profile updated successfully!");
      setIsEditingCompany(false);
    } catch (error) {
      toast.error("Failed to update company profile");
    } finally {
      setLoading(prev => ({ ...prev, company: false }));
    }
  };

  // Handle personal update
  const handlePersonalUpdate = async () => {
    setLoading(prev => ({ ...prev, personal: true }));
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Personal profile updated successfully!");
      setIsEditingPersonal(false);
    } catch (error) {
      toast.error("Failed to update personal profile");
    } finally {
      setLoading(prev => ({ ...prev, personal: false }));
    }
  };

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setCompanyData(prev => ({ ...prev, logo: imageUrl }));
        toast.success("Company logo updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/') && file.size <= 2 * 1024 * 1024) {
        const imageUrl = URL.createObjectURL(file);
        setPersonalData(prev => ({ ...prev, profileImage: imageUrl }));
        toast.success("Profile image updated!");
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        setUploadedFile(file);
        setErrors(prev => ({ ...prev, resume: "" }));
        toast.success("Document uploaded successfully!");
      } else {
        setErrors(prev => ({ ...prev, resume: "Please upload a valid PDF file under 5MB." }));
        setUploadedFile(null);
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "company", label: "Company Profile", icon: Building },
    { id: "personal", label: "Personal Profile", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield }
  ];

  return (
    <div className="p-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-3 py-2 text-sm font-medium border-b-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content Area */}
      <div>
        {/* Company Profile Tab */}
        {activeTab === "company" && (
          <div>
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Company Profile</h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">Manage your company information and branding</p>
              </div>
              <button
                onClick={() => isEditingCompany ? handleCompanyUpdate() : setIsEditingCompany(true)}
                disabled={loading.company}
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                {loading.company ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : isEditingCompany ? (
                  <>
                    <Check className="h-4 w-4" />
                    Save Changes
                  </>
                ) : (
                  "Edit Company"
                )}
              </button>
            </div>

            {/* Company Logo */}
            <div className="flex items-center gap-6 mb-8 pb-8 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <img
                  src={companyData.logo || "https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&h=256&q=80"}
                  alt="Company Logo"
                  className="w-24 h-24 rounded-lg object-cover border-4 border-white dark:border-gray-700 shadow-lg"
                />
                {isEditingCompany && (
                  <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg cursor-pointer opacity-0 hover:opacity-100 transition-opacity duration-200">
                    <Camera className="h-6 w-6 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{companyData.companyName}</h3>
                <p className="text-gray-600 dark:text-gray-400">{companyData.industry}</p>
                {isEditingCompany && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Click on logo to change company logo</p>
                )}
              </div>
            </div>

            {/* Company Form */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Company Name
                </label>
                <input
                  type="text"
                  value={companyData.companyName}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, companyName: e.target.value }))}
                  disabled={!isEditingCompany}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Company URL
                </label>
                <input
                  type="text"
                  value={companyData.companyUrl}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, companyUrl: e.target.value }))}
                  disabled={!isEditingCompany}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div className="lg:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={companyData.description}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, description: e.target.value }))}
                  disabled={!isEditingCompany}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Industry
                </label>
                <select
                  value={companyData.industry}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, industry: e.target.value }))}
                  disabled={!isEditingCompany}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                >
                  <option value="">Select Industry</option>
                  <option value="Technology">Technology</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Finance">Finance</option>
                  <option value="Education">Education</option>
                  <option value="Manufacturing">Manufacturing</option>
                  <option value="Retail">Retail</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Company Size
                </label>
                <select
                  value={companyData.companySize}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, companySize: e.target.value }))}
                  disabled={!isEditingCompany}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                >
                  <option value="">Select Size</option>
                  <option value="1-10">1-10 employees</option>
                  <option value="11-50">11-50 employees</option>
                  <option value="51-200">51-200 employees</option>
                  <option value="201-500">201-500 employees</option>
                  <option value="501-1000">501-1000 employees</option>
                  <option value="1000+">1000+ employees</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  value={companyData.website}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, website: e.target.value }))}
                  disabled={!isEditingCompany}
                  placeholder="https://example.com"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  value={companyData.location}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, location: e.target.value }))}
                  disabled={!isEditingCompany}
                  placeholder="e.g., San Francisco, CA"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>
            </div>
          </div>
        )}

        {/* Personal Profile Tab */}
        {activeTab === "personal" && (
          <div>
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Personal Profile</h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">Update your personal information</p>
              </div>
              <button
                onClick={() => isEditingPersonal ? handlePersonalUpdate() : setIsEditingPersonal(true)}
                disabled={loading.personal}
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                {loading.personal ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : isEditingPersonal ? (
                  <>
                    <Check className="h-4 w-4" />
                    Save Changes
                  </>
                ) : (
                  "Edit Profile"
                )}
              </button>
            </div>

            {/* Profile Image */}
            <div className="flex items-center gap-6 mb-8 pb-8 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <img
                  src={personalData.profileImage || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"}
                  alt="Profile"
                  className="w-24 h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
                />
                {isEditingPersonal && (
                  <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full cursor-pointer opacity-0 hover:opacity-100 transition-opacity duration-200">
                    <Camera className="h-6 w-6 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleProfileImageUpload}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{personalData.name}</h3>
                <p className="text-gray-600 dark:text-gray-400">{personalData.position}</p>
                {isEditingPersonal && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Click on image to change profile photo</p>
                )}
              </div>
            </div>

            {/* Personal Form */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  value={personalData.name}
                  onChange={(e) => setPersonalData(prev => ({ ...prev, name: e.target.value }))}
                  disabled={!isEditingPersonal}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={personalData.email}
                  onChange={(e) => setPersonalData(prev => ({ ...prev, email: e.target.value }))}
                  disabled={!isEditingPersonal}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={personalData.phone}
                  onChange={(e) => setPersonalData(prev => ({ ...prev, phone: e.target.value }))}
                  disabled={!isEditingPersonal}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Position
                </label>
                <input
                  type="text"
                  value={personalData.position}
                  onChange={(e) => setPersonalData(prev => ({ ...prev, position: e.target.value }))}
                  disabled={!isEditingPersonal}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>

              <div className="lg:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Department
                </label>
                <input
                  type="text"
                  value={personalData.department}
                  onChange={(e) => setPersonalData(prev => ({ ...prev, department: e.target.value }))}
                  disabled={!isEditingPersonal}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500"
                />
              </div>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === "security" && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Security Settings</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Update your password and security preferences</p>
            </div>

            <div className="max-w-2xl">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Change Password</h3>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Current Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.old ? "text" : "password"}
                      value={passwordData.oldPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, oldPassword: e.target.value }))}
                      className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                      placeholder="Enter your current password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, old: !prev.old }))}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      {showPasswords.old ? <EyeOffIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? "text" : "password"}
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                      placeholder="Enter a new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      {showPasswords.new ? <EyeOffIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? "text" : "password"}
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                      placeholder="Confirm your new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      {showPasswords.confirm ? <EyeOffIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                <button
                  onClick={handlePasswordChange}
                  disabled={loading.password}
                  className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                >
                  {loading.password ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Updating...
                    </>
                  ) : (
                    "Update Password"
                  )}
                </button>
              </div>

              {/* Security Tips */}
              <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-3">Password Security Tips</h4>
                <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-2">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Use at least 8 characters with a mix of letters, numbers, and symbols
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Avoid using personal information like names or birthdays
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Don't reuse passwords from other accounts
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Consider using a password manager for better security
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Notifications Tab */}
        {activeTab === "notifications" && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Notification Preferences</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Manage how you receive notifications and updates</p>
            </div>

            <div className="space-y-6">
              {[
                { key: "emailNotifications", label: "Email Notifications", description: "Receive notifications via email" },
                { key: "candidateApplications", label: "Candidate Applications", description: "Get notified about new candidate applications" },
                { key: "systemUpdates", label: "System Updates", description: "Receive notifications about system updates and maintenance" },
                { key: "weeklyReports", label: "Weekly Reports", description: "Receive weekly analytics and performance reports" },
                { key: "marketingEmails", label: "Marketing Emails", description: "Receive promotional emails and newsletters" }
              ].map((setting) => (
                <div key={setting.key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">{setting.label}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{setting.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={notificationSettings[setting.key]}
                      onChange={(e) => setNotificationSettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <button
                onClick={() => {
                  setLoading(prev => ({ ...prev, notifications: true }));
                  setTimeout(() => {
                    setLoading(prev => ({ ...prev, notifications: false }));
                    toast.success("Notification preferences updated!");
                  }, 1000);
                }}
                disabled={loading.notifications}
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                {loading.notifications ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  "Save Preferences"
                )}
              </button>
            </div>
          </div>
        )}

        {/* Privacy Tab */}
        {activeTab === "privacy" && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Privacy Settings</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Control who can see your information and how it's used</p>
            </div>

            <div className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Company Visibility</h3>
                <div className="space-y-3">
                  {[
                    { value: "public", label: "Public", description: "Anyone can view your company profile" },
                    { value: "verified", label: "Verified Users Only", description: "Only verified candidates can view your profile" },
                    { value: "private", label: "Private", description: "Only you can view your company profile" }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                      <input
                        type="radio"
                        name="companyVisibility"
                        value={option.value}
                        checked={privacySettings.companyVisibility === option.value}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, companyVisibility: e.target.value }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                      />
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{option.label}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{option.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                {[
                  { key: "showContactInfo", label: "Show Contact Information", description: "Allow candidates to see your contact details" },
                  { key: "allowCandidateMessages", label: "Allow Candidate Messages", description: "Let candidates send you messages directly" },
                  { key: "shareCompanyData", label: "Share Company Analytics", description: "Allow anonymized company data to be used for platform insights" }
                ].map((setting) => (
                  <div key={setting.key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">{setting.label}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{setting.description}</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={privacySettings[setting.key]}
                        onChange={(e) => setPrivacySettings(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <button
                  onClick={() => {
                    setLoading(prev => ({ ...prev, privacy: true }));
                    setTimeout(() => {
                      setLoading(prev => ({ ...prev, privacy: false }));
                      toast.success("Privacy settings updated!");
                    }, 1000);
                  }}
                  disabled={loading.privacy}
                  className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                >
                  {loading.privacy ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Saving...
                    </>
                  ) : (
                    "Save Settings"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployerSettings;
