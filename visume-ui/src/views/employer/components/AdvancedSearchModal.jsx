import React from "react";
import { Filter, X } from "lucide-react";

const SelectInput = ({ label, value, onChange, options }) => (
  <div className="space-y-2">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
    >
      <option value="">Select {label.toLowerCase()}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  </div>
);

const AdvancedSearchModal = ({
  isOpen,
  onClose,
  advancedFilters,
  setAdvancedFilters,
  onApplyFilters,
}) => {
  const [localFilters, setLocalFilters] = React.useState(advancedFilters);

  React.useEffect(() => {
    setLocalFilters(advancedFilters);
  }, [advancedFilters]);

  const handleChange = (field, value) => {
    setLocalFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleApply = () => {
    setAdvancedFilters(localFilters);
    onApplyFilters(localFilters);
    onClose();
  };

  if (!isOpen) return null;

  const experienceOptions = [
    { value: "0-1", label: "0-1 years Fresher" },
    { value: "2-3", label: "2-3 years Intermediate" },
    { value: "4-5", label: "4-5 years Experienced" },
    { value: "5+", label: "5+ years" },
  ];

  const salaryOptions = [
    { value: "0-5", label: "0-5 LPA" },
    { value: "5-10", label: "5-10 LPA" },
    { value: "10-15", label: "10-15 LPA" },
    { value: "15-20", label: "15-20 LPA" },
    { value: "20+", label: "20+ LPA" },
  ];

  const scoreOptions = [
    { value: "3", label: "30+ %" },
    { value: "5", label: "50+ %" },
    { value: "7", label: "70+ %" },
    { value: "8", label: "80+ %" },
    { value: "9", label: "90+ %" },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
      <div className="relative w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
        {/* Header */}
        <div className="mb-4 flex items-center justify-between">
          <h2 className="flex items-center gap-2 text-xl font-semibold text-gray-900">
            <Filter className="h-5 w-5" />
            Advanced Filters
          </h2>
          <button
            onClick={onClose}
            className="rounded-full p-1 hover:bg-gray-100"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Filter Options */}
        <div className="space-y-4">
          <SelectInput
            label="Experience"
            value={localFilters.experience}
            onChange={(value) => handleChange("experience", value)}
            options={experienceOptions}
          />

          <SelectInput
            label="Expected Salary"
            value={localFilters.expectedSalary}
            onChange={(value) => handleChange("expectedSalary", value)}
            options={salaryOptions}
          />

          <SelectInput
            label="Current Salary"
            value={localFilters.currentSalary}
            onChange={(value) => handleChange("currentSalary", value)}
            options={salaryOptions}
          />

          <SelectInput
            label="Profile Score"
            value={localFilters.score}
            onChange={(value) => handleChange("score", value)}
            options={scoreOptions}
          />
        </div>

        {/* Footer */}
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            onClick={handleApply}
            className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearchModal;
