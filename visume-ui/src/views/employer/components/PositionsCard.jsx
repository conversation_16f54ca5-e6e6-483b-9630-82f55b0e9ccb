import Dropdown from "components/dropdown";
import React from "react";
import {
  HiOutlineBriefcase,
  HiOutlineUserGroup,
  HiOutlineDotsVertical,
  HiX,
  HiOutlineLocationMarker,
  HiOutlineClock,
  HiOutlineTag,
} from "react-icons/hi";
import { useNavigate } from "react-router-dom";

const PositionsCard = ({ job }) => {
  const navigate = useNavigate();
  const skillsArray = job.skills
    ? job.skills.split(",").map((skill) => skill.trim())
    : [];

  return (
    <div className="p-4">
      <div className="flex items-start justify-between">
        {/* Left side: Job details */}
        <div className="flex flex-col space-y-3">
          {/* Title and Location */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 dark:text-white">
              {job.role}
            </h3>
            <div className="mt-1 flex items-center text-sm text-gray-600 dark:text-gray-300">
              <HiOutlineLocationMarker className="mr-1" />
              {job.candidateDetails.preferred_location}
            </div>
          </div>

          {/* Requirements */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <HiOutlineClock className="mr-1" />
              {job.experience_range} years
            </div>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <HiOutlineTag className="mr-1" />
              {job.employment_type || 'Full Time'}
            </div>
          </div>

          {/* Skills */}
          <div className="flex flex-wrap gap-2">
            {skillsArray.slice(0, 4).map((skill, index) => (
              <span
                key={index}
                className="rounded-full bg-brand-50 px-3 py-1 text-xs font-medium text-brand-600 dark:bg-brand-900/20 dark:text-brand-400"
              >
                {skill}
              </span>
            ))}
            {skillsArray.length > 4 && (
              <span className="rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-600 dark:bg-gray-800 dark:text-gray-300">
                +{skillsArray.length - 4} more
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PositionsCard;
