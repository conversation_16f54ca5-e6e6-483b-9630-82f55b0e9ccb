import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const ProfileSkelLoader = ({ keyValue }) => (
  <div
    key={keyValue}
    className="mb-1 flex items-center justify-between rounded-lg border border-gray-300 bg-white p-3 shadow-sm dark:border-navy-500 dark:bg-navy-800"
  >
    <div className="flex flex-col">
      <div className="mb-1 flex items-center">
        <Skeleton width={120} height={16} />
        <div className="ml-2 flex items-center text-sm text-gray-500 dark:text-gray-300">
          <Skeleton width={80} height={16} />
        </div>
      </div>
      <div className="mt-1 flex flex-wrap gap-2">
        <Skeleton width={50} height={24} className="rounded-full" />
        <Skeleton width={50} height={24} className="rounded-full" />
        <Skeleton width={50} height={24} className="rounded-full" />
        <Skeleton width={50} height={24} className="rounded-full" />
        <Skeleton width={40} height={24} className="rounded-full" />
      </div>
    </div>
    <div className="flex items-center gap-4">
      <Skeleton width={60} height={36} className="rounded-full" />
      <Skeleton width={20} height={20} circle />
    </div>
  </div>
);

export default ProfileSkelLoader;