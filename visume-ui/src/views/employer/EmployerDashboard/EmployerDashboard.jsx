import Cookies from "js-cookie";
import JobDescriptionList from "./JobDescriptionList";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { HiOutlineSparkles, HiOutlineBriefcase, HiOutlineTrash, HiOutlinePlus } from "react-icons/hi";
import {
  Header,
  StatsOverview,
  JobList,
  ProfileSkelLoader,
  JobDescriptionModal,
} from "./index";
import StatCard from "../components/StatCard";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import toast from "react-hot-toast";

const EmployerDashboard = () => {
  const jobDescListRef = useRef();
  const emp_id = Cookies.get("employerId");

  const handleJobDescriptionChange = () => {
    if (jobDescListRef.current && jobDescListRef.current.refresh) {
      jobDescListRef.current.refresh();
    }
  };
  const jstoken = Cookies.get("jstoken");
  
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =
    useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] =
    useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Default User1",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Job Description state
  const [dashboardJobDescription, setDashboardJobDescription] = useState(null);
  const [jdLoading, setJdLoading] = useState(false);
  const handleShortlist = async (id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You need to be an employer to shortlist profiles");
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
      // Remove the shortlisted profile from jobData
      setJobData((prev) =>
        prev.filter((profile) => profile.video_profile_id !== id)
      );
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };
  const [loadingId, setLoadingId] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const filterMatchingProfiles = (profiles, jobDescription) => {
      if (!profiles || !jobDescription) return [];

      return profiles
        .filter((profile) => {
          // Check role match (case-insensitive)
          const roleMatches =
            profile.role.toLowerCase() === jobDescription.role.toLowerCase();

          // Check skills match
          const profileSkills = profile.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const skillsMatch = jdSkills.some((skill) =>
            profileSkills.includes(skill)
          );

          return roleMatches && skillsMatch;
        })
        .sort((a, b) => {
          // Sort by number of matching skills
          const aSkills = a.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const bSkills = b.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const aMatches = jdSkills.filter((skill) =>
            aSkills.includes(skill)
          ).length;
          const bMatches = jdSkills.filter((skill) =>
            bSkills.includes(skill)
          ).length;

          return bMatches - aMatches;
        });
    };

    const getAllProfiles = async () => {
      try {
        setIsLoading(true);

        if (!dashboardJobDescription) {
          setJobData([]);
          setIsLoading(false);
          return;
        }

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();

        if (isMounted && res.candidateProfiles?.length) {
          const matchingProfiles = filterMatchingProfiles(
            res.candidateProfiles,
            dashboardJobDescription
          );
          setJobData(matchingProfiles);
        } else {
          setJobData([]);
        }
        setIsLoading(false);
      } catch (err) {
        console.error(`Error fetching profiles:`, err);
        if (isMounted) {
          setIsLoading(false);
          setJobData([]);
        }
      }
    };

    if (dashboardJobDescription) {
      getAllProfiles();
    }

    return () => {
      isMounted = false;
    };
  }, [emp_id, dashboardJobDescription]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Default User",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
            profile_picture: profileJson?.data?.profile_picture || "",
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    // Fetch job description for dashboard
    const fetchDashboardJobDescription = async () => {
      setJdLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setDashboardJobDescription(data.jobDescription || null);
        } else if (response.status === 404) {
          // No job description found, do not log error
          setDashboardJobDescription(null);
        } else {
          // Only log unexpected errors
          console.error(
            "Failed to fetch job description:",
            response.statusText
          );
          setDashboardJobDescription(null);
        }
      } catch (err) {
        console.error("Failed to fetch job description:", err);
        setDashboardJobDescription(null);
      }
      setJdLoading(false);
    };

    fetchCandidates();
    fetchDashboardJobDescription();
  }, [emp_id, isModalOpen]);

  // Delete handler for dashboard JD
  const handleDashboardDeleteJD = async () => {
    if (!dashboardJobDescription?._id) return;
    setJdLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          dashboardJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setDashboardJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    setJdLoading(false);
  };

  return (
    <>
      {jstoken ? (
        <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
          <div className="mx-auto max-w-7xl space-y-4 p-6">
          {/* Job Description List Section */}
            {/* Header Section - Clean and Modern */}
            <div className="rounded-xl bg-white p-4 shadow-sm ring-1 ring-slate-200 dark:bg-slate-800 dark:ring-slate-700">
              <div className="flex items-center justify-between">
                <Header empData={empData} />
                <StatsOverview
                  shortListedCandidatesCount={shortListedCandidatesCount}
                  unlockedCandidatesCount={unlockedCandidatesCount}
                  InterviewedCandidatesCount={InterviewedCandidatesCount}
                  offeredCandidatesCount={offeredCandidatesCount}
                  navigate={navigate}
                />
              </div>
            </div>

            {/* AI Candidate Matching Section - Professional Layout */}
            <div className="rounded-xl bg-white shadow-sm ring-1 ring-slate-200 dark:bg-slate-800 dark:ring-slate-700">
              {/* Section Header */}
              <div className="border-b border-slate-200 px-8 py-6 dark:border-slate-700">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-blue-500 to-purple-600">
                        <HiOutlineSparkles className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                          AI Candidate Matching
                        </h2>
                        <p className="text-xs text-slate-500 dark:text-slate-400">
                          {dashboardJobDescription
                            ? `Finding candidates for "${dashboardJobDescription.role}"`
                            : "Upload a job description to discover perfect matches"}
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* Contextual Upload Button */}
                  {dashboardJobDescription && (
                    <button
                      onClick={() => setModalOpen(true)}
                      className="inline-flex items-center rounded-lg border border-slate-300 bg-white px-4 py-2.5 text-sm font-medium text-slate-700 shadow-sm transition-all hover:bg-slate-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-700"
                    >
                      <HiOutlinePlus className="mr-2 h-4 w-4" />
                      New Job Description
                    </button>
                  )}
                </div>
              </div>

              {/* Content Area */}
              <div className="p-8">
                {jdLoading ? (
                  <div className="flex items-center justify-center py-16">
                    <div className="flex items-center space-x-3">
                      <div className="h-5 w-5 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                        Loading job descriptions...
                      </span>
                    </div>
                  </div>
                ) : dashboardJobDescription ? (
                  <div className="space-y-8">
                    {/* Current Job Description Card */}
                    <div className="rounded-lg border border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50">
                      <div className="flex items-center justify-between border-b border-slate-200 px-6 py-4 dark:border-slate-700">
                        <div>
                          <h3 className="text-lg font-medium text-slate-900 dark:text-white">
                            {dashboardJobDescription.role}
                          </h3>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            Active job description
                          </p>
                        </div>
                        <button
                          onClick={handleDashboardDeleteJD}
                          className="inline-flex items-center rounded-lg border border-red-300 bg-white px-3 py-2 text-sm font-medium text-red-700 shadow-sm transition-all hover:bg-red-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:bg-slate-800 dark:text-red-400 dark:hover:bg-red-900/20"
                        >
                          <HiOutlineTrash className="mr-1.5 h-4 w-4" />
                          Delete
                        </button>
                      </div>
                      <div className="grid gap-6 p-6 sm:grid-cols-3">
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-slate-500 dark:text-slate-400">
                            Experience Required
                          </dt>
                          <dd className="text-sm text-slate-900 dark:text-white">
                            {dashboardJobDescription.experience || "Not specified"}
                          </dd>
                        </div>
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-slate-500 dark:text-slate-400">
                            Location
                          </dt>
                          <dd className="text-sm text-slate-900 dark:text-white">
                            {dashboardJobDescription.location || "Not specified"}
                          </dd>
                        </div>
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-slate-500 dark:text-slate-400">
                            Required Skills
                          </dt>
                          <dd className="text-sm text-slate-900 dark:text-white">
                            {Array.isArray(dashboardJobDescription.skills)
                              ? dashboardJobDescription.skills.length
                                ? dashboardJobDescription.skills.join(", ")
                                : "Not specified"
                              : dashboardJobDescription.skills || "Not specified"}
                          </dd>
                        </div>
                      </div>
                    </div>

                    {/* Matching Results */}
                    <div className="space-y-6 h-full">
                      {isLoading ? (
                        <ProfileSkelLoader />
                      ) : jobData.length > 0 ? (
                        <>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="h-2 w-2 rounded-full bg-green-500"></div>
                              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                {jobData.length} candidate{jobData.length !== 1 ? 's' : ''} matched
                              </span>
                            </div>
                          </div>
                          <div className="grid gap-6 lg:grid-cols-2">
                            {jobData.map((profile, index) => (
                              <ProfileCard
                                key={profile.id || index}
                                {...profile}
                                score={profile.score}
                                onShortlist={() =>
                                  handleShortlist(profile.video_profile_id)
                                }
                                isLoading={loadingId === profile.video_profile_id}
                              />
                            ))}
                          </div>
                        </>
                      ) : (
                        <div className="rounded-lg border-2 border-dashed border-slate-300 p-12 text-center dark:border-slate-600">
                          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                            <HiOutlineBriefcase className="h-8 w-8 text-yellow-600 dark:text-yellow-500" />
                          </div>
                          <h3 className="mt-4 text-lg font-medium text-slate-900 dark:text-white">
                            No matching candidates
                          </h3>
                          <p className="mt-2 text-sm text-slate-500 dark:text-slate-400">
                            We couldn't find candidates matching your current requirements.
                            <br />
                            Consider broadening your criteria or check back later.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  /* Empty State - Professional Onboarding */
                  <div className="py-10">
                    <div className="mx-auto max-w-md text-center">
                      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600">
                        <HiOutlineSparkles className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="mt-6 text-xl font-semibold text-slate-900 dark:text-white">
                        Start Finding Great Candidates
                      </h3>
                      <p className="mt-3 text-sm leading-relaxed text-slate-500 dark:text-slate-400">
                        Upload your job description and let our AI match you with 
                        qualified Visumes based on skills, experience, and requirements.
                      </p>
                      <button
                        onClick={() => setModalOpen(true)}
                        className="mt-6 inline-flex items-center rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-3 text-sm font-semibold text-white shadow-lg transition-all hover:from-blue-600 hover:to-purple-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        <HiOutlineSparkles className="mr-2 h-5 w-5" />
                        Upload Job Description
                      </button>
      
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <JobDescriptionModal
            isOpen={isModalOpen}
            onClose={() => setModalOpen(false)}
          />
        </div>
      ) : (
        <div className="flex min-h-screen items-center justify-center bg-slate-50">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-slate-900">
              Please sign in to continue
            </h2>
            <p className="mt-2 text-sm text-slate-500">
              You need to be authenticated to access the dashboard
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default EmployerDashboard;