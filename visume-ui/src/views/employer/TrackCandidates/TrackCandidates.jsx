import React, { useEffect, useState } from "react";
import {
  HiOutlineShare,
  HiOutlineArchive,
  HiOutlinePencil,
  HiOutlineUpload,
  HiOutlineSearch,
  HiLockOpen,
  HiOutlineBriefcase,
  HiOutlineDownload,
  HiOutlineVideoCamera,
  HiDocumentText,
  HiOutlineDocumentText,
  HiHeart,
  HiLockClosed,
} from "react-icons/hi";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import Loader from "components/Loader";
import toast from "react-hot-toast";

import CandidateRow from "./CandidateRow";

const TrackCandidates = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const tabName = params.get("tab");
  const [loader, setLoader] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [shortListedCandidates, setShortListedCandidates] = useState([]);
  const [unlockedData, setUnlockedData] = useState([]);
  const [activeTabName, setActiveTabName] = useState("Shortlisted");
  const [loadingId, setLoadingId] = useState(null);
  const [noShortlistedProfiles, setNoShortlistedProfiles] = useState(false);
  const handleSearchToggle = () => setShowSearch((prev) => !prev);

  const handleSearchChange = (e) => setSearchTerm(e.target.value);

  const handleStatusChange = async (video_profile_id, newStatus) => {
    if (newStatus !== "Unlocked") {
      toast.error("This function is not ready yet");
      return;
    }

    if (loadingId === video_profile_id) return;
    setLoadingId(video_profile_id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to update video profile status"
        );
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unlockVideoProfile`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, video_profile_id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        return toast.error(msg.message);
      }

      const data = await response.json();
      toast.success(data.message);
      await fetchCandidates();
    } catch (error) {
      toast.error("Error updating video resume status");
      console.error("Error during updating video resume status:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const handleClickOutside = (event) => setShowSearch(false);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleUnlockCandidate = (candidateId) => {
    const updateCandidates = (prevData) =>
      prevData.map((candidate) =>
        candidate.id === candidateId
          ? { ...candidate, unlocked: !candidate.unlocked }
          : candidate
      );

    if (activeTabName === "Shortlisted") {
      setShortListedCandidates(updateCandidates);
    } else {
      setUnlockedData(updateCandidates);
    }
  };

  const filteredCandidates = (
    activeTabName === "Shortlisted"
      ? shortListedCandidates
      : activeTabName === "Unlocked"
      ? unlockedData
      : []
  ).filter((candidate) => {
    // Support both candidate.cand_name and candidate.jobseeker?.cand_name
    const name =
      candidate.cand_name ||
      (candidate.jobseeker && candidate.jobseeker.cand_name) ||
      "";
    return name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  console.log("Filtered candidates for tab", activeTabName, ":", filteredCandidates);
  const [currentPage, setCurrentPage] = useState(1);
  const candidatesPerPage = 10;
  const totalPages = Math.ceil(filteredCandidates.length / candidatesPerPage);
  const currentCandidates = filteredCandidates.slice(
    (currentPage - 1) * candidatesPerPage,
    currentPage * candidatesPerPage
  );

  const handlePageChange = (page) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const unShortlistCandidate = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(cand_id);
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to UnShortlist profiles"
        );
      }
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unshortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );
      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }
      const data = await response.json();
      toast.success(data.message);
      await fetchCandidates();
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const fetchCandidates = async () => {
    setLoader(true);
    const emp_id = Cookies.get("employerId");
    if (!emp_id) {
      toast.error("You are not an employer");
      navigate("/");
      return;
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      );
      if (response.status === 404) {
        setNoShortlistedProfiles(true);
        setShortListedCandidates([]);
        setUnlockedData([]);
        setLoader(false);
        return;
      } else {
        setNoShortlistedProfiles(false);
      }
      const data = await response.json();

      if (data.data) {
        console.log("Fetched candidates:", data.data);
        const shortlistedCandidates = data.data.filter(
          (e) => e.status === "shortlisted"
        );
        const unlockedCandidates = data.data.filter(
          (e) => e.status === "unlocked"
        );
        setShortListedCandidates(shortlistedCandidates);
        setUnlockedData(unlockedCandidates);
        if (shortlistedCandidates.length > 0) {
          setActiveTabName("Shortlisted");
        } else if (unlockedCandidates.length > 0) {
          setActiveTabName("Unlocked");
        }
      }
    } catch (err) {
      console.error("Error fetching shortlisted profiles:", err);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, [navigate]);

  useEffect(() => {
    if (tabName) {
      setActiveTabName(tabName);
    }
  }, [tabName]);

  return (
    <div className="rounded-2xl bg-white p-6 shadow-lg dark:bg-navy-700">
      {/* Job Info Section */}
      <div className="mb-10 flex flex-col md:flex-row items-center justify-between">

        {loader && <Loader text={"Fetching Shortlisted Profiles"} />}
        
        <div className="flex items-center space-x-4">
          <h3 className="text-sm md:text-xl font-semibold">
            Track Candidates
          </h3>
        </div>

        <div className="flex flex-col space-y-4 md:flex items-center space-x-5">
          <button
            onClick={handleSearchToggle}
            className="flex items-center space-x-1 text-gray-600 hover:text-gray-800"
          >
            <HiOutlineSearch className="text-lg hidden md:block" />
            <span className="hidden md:block">Search</span>
          </button>
          {showSearch && (
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearchChange}
              className="text-sm md:text-base ml-2 rounded-full border border-brand-300 px-2 py-1"
              placeholder="Search candidates..."
            />
          )}
          <button className="flex items-center space-x-1 rounded-lg bg-brand-500 px-3 py-1 text-white hover:bg-brand-600">
            <HiOutlineDownload className="text-sm md:text-base " />
            <span className="text-xs md:text-base ">Export</span>
          </button>
        </div>

      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 space-y-4 md:space-x-4">
        {["Shortlisted", "Unlocked", "Interview", "Offers"].map((tab) => {
          // Determine if the tab should be disabled
          const isDisabled =
            (tab === "Shortlisted" && shortListedCandidates.length === 0) ||
            (tab === "Unlocked" && unlockedData.length === 0) ||
            tab === "Interview" ||
            tab === "Offers";

          return (
            <button
              key={tab}
              className={`relative px-4 py-2 text-sm ${
                activeTabName === tab
                  ? "border-b-2 border-blue-500 font-semibold text-blue-600"
                  : `${isDisabled ? "text-gray-500" : "text-gray-800"}`
              } ${isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
              onClick={() => {
                if (!isDisabled) {
                  setActiveTabName(tab);
                }
              }}
              disabled={isDisabled} // Disable button based on isDisabled condition
            >
              {tab}
              {tab === "Shortlisted" ? (
                <span className="absolute right-1 top-0 flex h-4 w-4 -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-red-500 text-[0.8rem] text-white">
                  {shortListedCandidates.length}
                </span>
              ) : tab === "Unlocked" ? (
                <span className="absolute right-1 top-0 flex h-4 w-4 -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-red-500 text-[0.8rem] text-white">
                  {unlockedData.length}
                </span>
              ) : (
                <span className="absolute right-1 top-0 flex h-4 w-4 -translate-y-1/2 translate-x-1/2 transform items-center justify-center rounded-full bg-red-500 text-[0.8rem] text-white">
                  0
                </span>
              )}
            </button>
          );
        })}
      </div>

      {noShortlistedProfiles ? (
        <span className="flex py-5">
          No shortlisted profiles. Shortlist candidates using Profile Search.
        </span>
      ) : filteredCandidates.length > 0 ? (
        <>
          {/* Candidates Table */}
          <div className="min-h-[55vh] overflow-x-auto">
            <table className="min-w-full table-auto">
              <thead>
                <tr className="text-left text-sm font-semibold text-gray-600 dark:text-gray-300">
                  <th className="p-2">Candidate</th>
                  <th className="p-2">Role</th>
                  <th className="p-2">Skills</th>
                  <th className="p-2">Scores</th>
                  <th className="p-2">Contact</th>
                  <th className="p-2">Status</th>
                  <th className="p-2">Actions</th>
                </tr>
              </thead>
              <tbody className="text-sm">
                {currentCandidates.map((candidate) => (
                  <CandidateRow
                    tabName={activeTabName}
                    loadingId={loadingId}
                    key={candidate?.id}
                    candidate={candidate}
                    onStatusChange={handleStatusChange}
                    onToggleUnlock={toggleUnlockCandidate}
                    unShortlistCandidate={unShortlistCandidate}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="mt-4 flex items-center justify-center">
            <span
              onClick={() => handlePageChange(currentPage - 1)}
              className={`cursor-pointer px-3 py-1 ${
                currentPage === 1
                  ? "cursor-not-allowed text-gray-300"
                  : "text-blue-500 hover:underline"
              }`}
            >
              Previous
            </span>
            <div className="flex space-x-2">
              {Array.from({ length: totalPages }, (_, index) => (
                <button
                  key={index + 1}
                  onClick={() => handlePageChange(index + 1)}
                  className={`rounded-full px-3 py-1 ${
                    currentPage === index + 1
                      ? "bg-brand-500 text-white"
                      : "bg-gray-200 hover:bg-gray-300"
                  }`}
                >
                  {index + 1}
                </button>
              ))}
            </div>
            <span
              onClick={() => handlePageChange(currentPage + 1)}
              className={`cursor-pointer px-3 py-1 ${
                currentPage === totalPages
                  ? "cursor-not-allowed text-gray-300"
                  : "text-blue-500 hover:underline"
              }`}
            >
              Next
            </span>
          </div>
        </>
      ) : (
        <span className="flex py-5">No {activeTabName} Profile Found.</span>
      )}
    </div>
  );
};


export default TrackCandidates;
