import Cookies from 'js-cookie'
import React, { useEffect, useState } from 'react'

const ViewResume = () => {
  const candId = Cookies.get("candId")
  const [resume, setResume] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchResume = async () => {
      try {
        setLoading(true)
        const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`)
        const data = await response.json()

        const strippedResume = data.candidateProfile?.[0]?.stripped_resume
        if (!strippedResume) {
          throw new Error("Resume not available or still processing")
        }

        // Since backend already sends parsed data, no need to parse again
        const resumeData = strippedResume
        setResume(resumeData)
        setError(null)
      } catch (error) {
        console.error('Error fetching resume:', error)
        setError(error.message)
        setResume(null)
      } finally {
        setLoading(false)
      }
    }
    fetchResume()
  }, [candId])

  if (loading) return <div className="p-5">Loading resume...</div>
  if (error) return <div className="p-5 text-red-500">{error}</div>
  if (!resume) return <div className="p-5">No resume data available</div>

  return (
    <div className="p-5 max-w-3xl mx-auto">
      <div className="space-y-6">
        {/* Personal Information */}
        <section>
          <h2 className="text-2xl font-bold mb-3">Personal Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <p><span className="font-semibold">Name:</span> {resume.personal_info?.name}</p>
            <p><span className="font-semibold">Email:</span> {resume.personal_info?.email}</p>
            <p><span className="font-semibold">Phone:</span> {resume.personal_info?.phone}</p>
            <p><span className="font-semibold">Location:</span> {resume.personal_info?.location}</p>
          </div>
        </section>

        {/* Education */}
        <section>
          <h2 className="text-2xl font-bold mb-3">Education</h2>
          <div className="space-y-4">
            {resume.education?.map((edu, index) => (
              <div key={index} className="border-l-4 border-gray-200 pl-4">
                <p className="font-semibold">{edu.degree}</p>
                <p>{edu.institution}</p>
                <p className="text-gray-600">{edu.year}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Experience */}
        <section>
          <h2 className="text-2xl font-bold mb-3">Experience</h2>
          <div className="space-y-6">
            {resume.experience?.map((exp, index) => (
              <div key={index} className="border-l-4 border-gray-200 pl-4">
                <p className="font-semibold">{exp.title}</p>
                <p>{exp.company}</p>
                <p className="text-gray-600">{exp.duration}</p>
                <ul className="list-disc ml-5 mt-2">
                  {exp.responsibilities?.map((resp, i) => (
                    <li key={i}>{resp}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </section>

        {/* Skills */}
        <section>
          <h2 className="text-2xl font-bold mb-3">Skills</h2>
          <div className="flex flex-wrap gap-2">
            {resume.skills?.map((skill, index) => (
              <span key={index} className="bg-gray-100 px-3 py-1 rounded-full">
                {skill}
              </span>
            ))}
          </div>
        </section>

        {/* Certifications */}
        {resume.certifications?.length > 0 && (
          <section>
            <h2 className="text-2xl font-bold mb-3">Certifications</h2>
            <ul className="list-disc ml-5">
              {resume.certifications.map((cert, index) => (
                <li key={index}>{cert}</li>
              ))}
            </ul>
          </section>
        )}
      </div>
    </div>
  )
}

export default ViewResume
