import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../smaller_comp/Button';
import { CheckCircle, Video, Home, Play, Download } from 'lucide-react';

export default function EndInterviewSection() {
  const navigate = useNavigate();
  const [videoUrl, setVideoUrl] = useState(null);

  useEffect(() => {
    // Get the saved video from localStorage
    const savedVideo = localStorage.getItem('interviewVideo');
    if (savedVideo) {
      setVideoUrl(savedVideo);
    }
  }, []);

  const handleNavigate = () => {
    // Clean up the video from localStorage before navigating
    localStorage.removeItem('interviewVideo');
    navigate("/candidate/dashboard");
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="mx-auto max-w-4xl">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 shadow-lg">
            <CheckCircle className="h-10 w-10 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
            🎉 Congratulations!
          </h1>
          <h2 className="text-xl text-gray-700 dark:text-gray-300 mb-2">
            Your Video Resume Has Been Successfully Submitted
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Your interview has been processed and your video resume is now live in our system.
            Employers can now discover your profile and reach out to you.
          </p>
        </div>

        {/* Video Preview Card */}
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden mb-8">
          <div className="p-4 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/50 dark:to-purple-900/50 rounded-lg">
                <Video className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Your Final Video Resume
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Review your submitted interview recording
                </p>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="relative w-full rounded-lg overflow-hidden bg-gray-900 shadow-lg" style={{ aspectRatio: '16/9' }}>
              {videoUrl ? (
                <video
                  src={videoUrl}
                  controls
                  className="w-full h-full object-cover"
                  controlsList="nodownload"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                  <div className="text-center text-white">
                    <Play className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm opacity-75">Video processing...</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-xl mb-8">
          <div className="p-4 border-b border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
              What happens next?
            </h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 dark:text-blue-400 font-bold">1</span>
                </div>
                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Profile Goes Live</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  Your video resume is now searchable by employers
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 dark:text-purple-400 font-bold">2</span>
                </div>
                <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Get Discovered</h4>
                <p className="text-sm text-purple-800 dark:text-purple-200">
                  Employers will find you based on your skills and experience
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 dark:text-green-400 font-bold">3</span>
                </div>
                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Receive Offers</h4>
                <p className="text-sm text-green-800 dark:text-green-200">
                  Get contacted directly for job opportunities
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="text-center">
          <button
            onClick={handleNavigate}
            className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <Home className="w-5 h-5" />
            Back to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}