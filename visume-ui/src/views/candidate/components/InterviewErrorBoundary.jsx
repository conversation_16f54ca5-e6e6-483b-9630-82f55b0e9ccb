import React from 'react';
import toast from 'react-hot-toast';

class InterviewErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Interview error:', { error, errorInfo });
    toast.error('Interview error occurred');
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
            <h2 className="mb-4 text-xl font-semibold text-red-700">
              Interview Error
            </h2>
            <p className="text-red-600">
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Restart Interview
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default InterviewErrorBoundary;