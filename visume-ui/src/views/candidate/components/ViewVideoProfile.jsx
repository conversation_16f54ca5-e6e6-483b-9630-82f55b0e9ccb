import Loader from "../../../components/Loader";
import React, { useDeferredValue, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BsExclamationCircle } from "react-icons/bs";
import { useNavigate, useParams } from "react-router-dom";

function ViewVideoProfile() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [data, setData] = useState(null); // use `null` to make checking for data easier
  const [loading, setLoading] = useState(true); // default loading is true
  const [toolTip, showToolTip] = useState(false);
  const [videoProfileId, setVideoProfileId] = useState("");

  const getProfileData = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/video-resume-data/${videoProfileId}`, // Update this to your correct API URL
        {
          method: "GET",
        }
      );

      if (!response.ok) {
        navigate("/candidate/video-resume");
        throw new Error(response.statusText);
      }

      const res = await response.json();
      setData(res.data);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false); // Ensure the loading state is set to false after fetching
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/video-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile deleted successfully");
        navigate("/candidate/video-resume");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  useEffect(() => {
    if (id) {
      setVideoProfileId(id);
    }
  }, [id]);

  useEffect(() => {
    if (videoProfileId) {
      getProfileData();
    }
  }, [videoProfileId]);

  if (loading) {
    return <Loader text={"Getting Video Profile Data"} />;
  }

  if (!data) {
    return <div>No video profile data found.</div>;
  }

  const formatDate = (input) => {
    if (!input) return "N/A";
    let date;
    // If input is an object (e.g., Firestore Timestamp)
    if (typeof input === "object") {
      // Try common timestamp fields
      if ("seconds" in input) {
        date = new Date(input.seconds * 1000);
      } else if ("_seconds" in input) {
        date = new Date(input._seconds * 1000);
      } else {
        return "N/A";
      }
    } else {
      // Try string/number
      date = new Date(input);
      if (isNaN(date.getTime()) && !isNaN(Number(input))) {
        date = new Date(Number(input));
      }
    }
    if (!date || isNaN(date.getTime())) return "N/A";
    const options = { year: "numeric", month: "long", day: "numeric" };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <div className="flex h-full min-h-screen w-full flex-col items-center justify-start bg-gradient-to-br from-blue-50 via-gray-50 to-gray-100 px-2 py-8 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 lg:px-12">
      <div className="flex h-full w-full max-w-6xl flex-col rounded-2xl bg-white/90 p-8 shadow-2xl transition-all duration-300 dark:bg-gray-900/90">
        {/* Back Button */}
        <div className="mb-6 flex items-center gap-2">
          <button
            className="flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-2 transition hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
            onClick={() => navigate("/candidate/dashboard")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-blue-600 dark:text-blue-300"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 111.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            <span className="font-semibold text-blue-700 dark:text-blue-200">
              Back
            </span>
          </button>
        </div>
        <h1 className="mb-8 text-3xl font-extrabold tracking-tight text-blue-900 dark:text-blue-100">
          Video Resume Review
        </h1>
        <div className="flex flex-col gap-8 lg:flex-row">
          {/* Left Section: Video & Details */}
          <div className="flex w-full min-w-0 flex-col items-start gap-8 lg:w-3/5">
            <div className="relative h-[55vh] w-full overflow-hidden rounded-2xl border border-blue-100 bg-gray-900 shadow-lg dark:border-blue-800">
              <video
                src={data?.video_url}
                className="h-full w-full object-cover"
                controls
                poster="/src/assets/img/videores-illustration.png"
              />
              <div className="absolute right-2 top-2 rounded-lg bg-blue-600/80 px-3 py-1 text-xs font-semibold text-white shadow">
                {data.role}
              </div>
            </div>
            {/* Next Focus Areas Card (from ReviewVideoProfile) */}
            {JSON.parse(data.score).Next_Focus_Areas &&
              JSON.parse(data.score).Next_Focus_Areas.length > 0 && (
                <div className="mt-2 w-full rounded-2xl border border-indigo-100 bg-gradient-to-br from-indigo-50 to-blue-50 p-6 shadow-md dark:border-indigo-700 dark:bg-indigo-900/20">
                  <div className="mb-2 flex items-center gap-3">
                    <svg
                      className="h-6 w-6 text-indigo-600 dark:text-indigo-400"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"
                      />
                    </svg>
                    <h2 className="text-lg font-bold text-indigo-900 dark:text-indigo-100">
                      Next Focus Areas
                    </h2>
                  </div>
                  <ul className="text-md list-disc space-y-2 pl-5 text-gray-700 dark:text-gray-300">
                    {JSON.parse(data.score).Next_Focus_Areas.map(
                      (area, idx) => (
                        <li key={idx}>{area}</li>
                      )
                    )}
                  </ul>
                </div>
              )}
            {JSON.parse(data.score).evaluation &&
              JSON.parse(data.score).evaluation.length > 0 && (
                <details className="mt-2 w-full rounded-2xl border border-indigo-100 bg-gradient-to-br from-indigo-50 to-blue-50 p-6 shadow-md dark:border-indigo-700 dark:bg-indigo-900/20">
                  <summary className="mb-2 flex items-center gap-3 cursor-pointer text-lg font-bold text-indigo-900 dark:text-indigo-100">
                    <svg className="h-6 w-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"/>
                    </svg>
                    <span>
                      <span className="underline decoration-dotted decoration-indigo-500 underline-offset-4">Click here for Detailed Question-wise Overview</span>
                      <span className="ml-2 inline-block align-middle text-indigo-500">&#x25BC;</span>
                    </span>
                  </summary>
                  <div className="space-y-4">
                    {JSON.parse(data.score).evaluation.map((item, index) => (
                      <div
                        key={index}
                        className="rounded-lg border border-blue-200 bg-white/70 p-4 shadow dark:border-blue-800 dark:bg-blue-900/30"
                      >
                        <p className="text-base font-medium text-blue-900 dark:text-blue-100">
                          <span className="font-bold">Question:</span>{" "}
                          {item.Question}
                        </p>
                        <div className="mt-1 text-base text-blue-800 dark:text-blue-200">
                          <span className="font-bold">Your Answer:</span>{" "}
                          {/^(import|const|let|function|class|interface|type|export|async|await|\s*<\w)/.test(
                            item.Your_Answer?.trim()
                          ) ? (
                            <pre className="overflow-x-auto rounded bg-gray-100 p-2 dark:bg-gray-800">
                              <code className="language-jsx">
                                {item.Your_Answer}
                              </code>
                            </pre>
                          ) : (
                            <span>{item.Your_Answer || "Not provided"}</span>
                          )}
                        </div>
                        <p className="mt-1 text-base text-blue-800 dark:text-blue-200">
                          <span className="font-bold">Expected Answer:</span>{" "}
                          {item.Expected_Answer}
                        </p>
                      </div>
                    ))}
                  </div>
                </details>
              )}
          </div>
          {/* Right Section: Scores & Actions */}
          <div className="flex min-h-[55vh] w-full flex-col space-y-6 rounded-2xl border border-gray-100 bg-white/80 p-8 shadow-lg dark:border-gray-700 dark:bg-gray-800/80 lg:w-2/5">
            {/* Created At */}
            <div className="flex items-center gap-3 rounded-xl bg-blue-50 p-4 shadow-sm dark:bg-blue-900/30">
              <svg
                className="h-6 w-6 text-blue-600 dark:text-blue-300"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <div>
                <h3 className="text-lg font-bold text-blue-900 dark:text-blue-100">
                  Created At
                </h3>
                <span className="text-md font-normal text-gray-700 dark:text-gray-300">
                  {formatDate(data.created_at)}
                </span>
              </div>
            </div>

            {/* Role */}
            <div className="flex items-center gap-3 rounded-xl bg-blue-50 p-4 shadow-sm dark:bg-blue-900/30">
              <svg
                className="h-6 w-6 text-blue-600 dark:text-blue-300"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
              <div>
                <h3 className="text-lg font-bold text-blue-900 dark:text-blue-100">
                  Role
                </h3>
                <span className="text-md font-normal text-gray-700 dark:text-gray-300">
                  {data.role}
                </span>
              </div>
            </div>

            {/* Your Scores */}
            <div className="flex flex-col gap-4 rounded-xl bg-blue-50 p-4 shadow-sm dark:bg-blue-900/30">
              <h3 className="flex items-center gap-2 text-lg font-bold text-blue-900 dark:text-blue-100">
                <svg
                  className="h-6 w-6 text-blue-600 dark:text-blue-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 20l9-5-9-5-9 5 9 5z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 12V4m0 0L2 9m10-5l10 5"
                  />
                </svg>
                Your Scores
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Skill Score */}
                <div
                  className={`flex flex-col items-center justify-center rounded-lg border-t-4 bg-white px-3 py-2 shadow dark:bg-gray-900 ${
                    JSON.parse(data.score).score.Skill_Score < 5
                      ? "border-red-500"
                      : JSON.parse(data.score).score.Skill_Score <= 7
                      ? "border-yellow-500"
                      : "border-green-500"
                  }`}
                >
                  <span
                    className={`mb-1 rounded px-3 py-1 text-lg font-bold ${
                      JSON.parse(data.score).score.Skill_Score < 5
                        ? "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-200"
                        : JSON.parse(data.score).score.Skill_Score <= 7
                        ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-800 dark:text-yellow-200"
                        : "bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-200"
                    }`}
                  >
                    {JSON.parse(data.score).score.Skill_Score || 0}
                  </span>
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    Skill
                  </span>
                </div>
                {/* Communication Score */}
                <div
                  className={`flex flex-col items-center justify-center rounded-lg border-t-4 bg-white px-3 py-2 shadow dark:bg-gray-900 ${
                    JSON.parse(data.score).score.Communication_Score < 5
                      ? "border-red-500"
                      : JSON.parse(data.score).score.Communication_Score <= 7
                      ? "border-yellow-500"
                      : "border-green-500"
                  }`}
                >
                  <span
                    className={`mb-1 rounded px-3 py-1 text-lg font-bold ${
                      JSON.parse(data.score).score.Communication_Score < 5
                        ? "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-200"
                        : JSON.parse(data.score).score.Communication_Score <= 7
                        ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-800 dark:text-yellow-200"
                        : "bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-200"
                    }`}
                  >
                    {JSON.parse(data.score).score.Communication_Score || 0}
                  </span>
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    Communication
                  </span>
                </div>
                {/* Overall Score */}
                <div
                  className={`flex flex-col items-center justify-center rounded-lg border-t-4 bg-white px-3 py-2 shadow dark:bg-gray-900 ${
                    JSON.parse(data.score).score.Overall_Score < 5
                      ? "border-red-500"
                      : JSON.parse(data.score).score.Overall_Score <= 7
                      ? "border-yellow-500"
                      : "border-green-500"
                  }`}
                >
                  <span
                    className={`mb-1 rounded px-3 py-1 text-lg font-bold ${
                      JSON.parse(data.score).score.Overall_Score < 5
                        ? "bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-200"
                        : JSON.parse(data.score).score.Overall_Score <= 7
                        ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-800 dark:text-yellow-200"
                        : "bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-200"
                    }`}
                  >
                    {JSON.parse(data.score).score.Overall_Score || 0}
                  </span>
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    Overall
                  </span>
                </div>
              </div>
            </div>

            {/* Suggestions */}
            <div className="flex items-start gap-3 rounded-xl bg-blue-50 p-4 shadow-sm dark:bg-blue-900/30">
              <svg
                className="h-6 w-6 text-blue-600 dark:text-blue-300"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"
                />
              </svg>
              <div>
                <h3 className="text-lg font-bold text-blue-900 dark:text-blue-100">
                  Suggestions
                </h3>
                <p className="text-md whitespace-pre-line text-gray-700 dark:text-gray-300">
                  {JSON.parse(data.score).Suggestions}
                </p>
              </div>
            </div>

            {/* Status */}
            <div className="group relative flex items-center gap-3 rounded-xl bg-blue-50 p-4 shadow-sm dark:bg-blue-900/30">
              <svg
                className="h-6 w-6 text-blue-600 dark:text-blue-300"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 8v4l3 3"
                />
              </svg>
              <div className="flex flex-col">
                <h3 className="flex items-center gap-2 text-lg font-bold text-blue-900 dark:text-blue-100">
                  Status
                  <BsExclamationCircle
                    onMouseEnter={() => showToolTip(true)}
                    onMouseLeave={() => showToolTip(false)}
                    className="cursor-pointer"
                  />
                  {toolTip && (
                    <div className="absolute bottom-full left-0 mb-2 block w-max rounded-md bg-blue-900 bg-opacity-80 px-3 py-1 text-sm text-white shadow-lg">
                      Status will be Active if score is above 5, otherwise
                      Inactive.
                    </div>
                  )}
                </h3>
                <span
                  className={`mt-2 w-fit rounded-lg px-3 py-1 text-sm font-semibold ${
                    data.status === "active"
                      ? "bg-green-100 text-green-700 dark:bg-green-800 dark:text-green-200"
                      : "bg-red-100 text-red-700 dark:bg-red-800 dark:text-red-200"
                  }`}
                >
                  {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
                </span>
              </div>
            </div>

            {/* Delete Button */}
            <div className="mt-8 flex justify-end">
              <button
                className="inline-flex items-center gap-3 rounded-xl bg-gradient-to-tr from-red-500 to-red-700 px-6 py-4 text-lg font-extrabold text-white shadow-xl transition-all duration-200 hover:scale-105 hover:from-red-600 hover:to-red-800 focus:outline-none focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900"
                onClick={() => {
                  if (
                    window.confirm(
                      "Are you sure you want to delete this video resume? This action cannot be undone."
                    )
                  ) {
                    handleDelete();
                  }
                }}
                aria-label="Delete Video Resume"
              >
                <svg
                  className="h-7 w-7"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 7h12M9 7V5a3 3 0 013-3 3 3 0 013 3v2m-7 0h10a2 2 0 012 2v11a2 2 0 01-2 2H7a2 2 0 01-2-2V9a2 2 0 012-2zm2 4v6m4-6v6"
                  />
                </svg>
                Delete Video Resume
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ViewVideoProfile;
