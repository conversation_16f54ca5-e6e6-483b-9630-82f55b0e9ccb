import React, { useState, useEffect } from "react";
import Button from "../smaller_comp/Button";
import Loader from "components/Loader";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { CheckCircle, FileText, Shield, Eye, Mic, Monitor } from "lucide-react";

export default function TermsAndConditionsSection({
  hasAcceptedTerms,
  setHasAcceptedTerms,
  isInterviewMode,
  formData,
  isLoading,
  onAccept,
  handleSubmit,
}) {
  // Prevent submit if jobRole is not selected
  const handleSubmitWithRoleCheck = (e) => {
    if (!formData || !formData.jobRole || formData.jobRole.trim() === "") {
      toast.error("Please select a role before proceeding.");
      return;
    }
    if (typeof handleSubmit === "function") {
      handleSubmit(e);
    }
  };
  return (
    <div className="space-y-6 max-w-2xl mx-auto p-6">
      {/* Review Section - Only show in form submission mode */}
      {!isInterviewMode && formData && (
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
          <h2 className="mb-3 flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-white">
            <Eye className="h-4 w-4" />
            Review Your Information
          </h2>
          <div className="flex flex-col gap-2 text-sm w-full">
            <div className="flex flex-row items-center gap-2">
              <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Role:</span>
              <span className="text-gray-900 dark:text-white">{formData.jobRole}</span>
            </div>
            <div className="flex flex-row items-center gap-2">
              <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Experience:</span>
              <span className="text-gray-900 dark:text-white">{formData.experience} years</span>
            </div>
            <div className="flex flex-row items-center gap-2">
              <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Company Type:</span>
              <span className="text-gray-900 dark:text-white capitalize">{formData.companyType.replace("_", " ")}</span>
            </div>
            <div className="flex flex-row items-center gap-2">
              <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Skills:</span>
              <span className="flex flex-wrap gap-1">
                {formData.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
                  >
                    {skill}
                  </span>
                ))}
              </span>
            </div>
            {(formData.experience === "2-3" || formData.experience === "3-5") && (
              <>
                <div className="flex flex-row items-center gap-2">
                  <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Current Salary:</span>
                  <span className="text-gray-900 dark:text-white">{formData.salary.current} LPA</span>
                </div>
                <div className="flex flex-row items-center gap-2">
                  <span className="font-medium text-gray-600 dark:text-gray-400 min-w-[120px]">Expected Salary:</span>
                  <span className="text-gray-900 dark:text-white">{formData.salary.expected} LPA</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Terms Section */}
      <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20">
        <h2 className="mb-3 flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-white">
          <Shield className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
          Terms and Conditions
        </h2>
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="rounded bg-yellow-100 p-1 dark:bg-yellow-900/50">
              <Mic className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              By proceeding, you consent to sharing your screen recording,
              microphone audio, and system audio with employers for interview
              purposes.
            </p>
          </div>
          <div className="flex items-start gap-3">
            <div className="rounded bg-yellow-100 p-1 dark:bg-yellow-900/50">
              <Monitor className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Please ensure you are in a quiet environment, free from
              distractions, to maintain interview quality.
            </p>
          </div>
          <div className="flex items-start gap-3">
            <div className="rounded bg-yellow-100 p-1 dark:bg-yellow-900/50">
              <CheckCircle className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Note: You will enter full screen mode after accepting these terms.
            </p>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <div className="flex justify-center pt-2">
        <button
          onClick={isInterviewMode ? onAccept : (handleSubmit ? handleSubmitWithRoleCheck : undefined)}
          disabled={
            isLoading ||
            (!isInterviewMode && (!formData || !formData.jobRole || formData.jobRole.trim() === ""))
          }
          className={`inline-flex items-center gap-2 rounded-lg px-6 py-3 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            isLoading || (!isInterviewMode && !formData)
              ? "cursor-not-allowed bg-gray-400 text-white"
              : "to-emerald-600 hover:to-emerald-700 transform bg-gradient-to-r from-green-600 text-white shadow-lg hover:-translate-y-0.5 hover:from-green-700 hover:shadow-xl"
          }`}
        >
          {isLoading ? (
            <>
              <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
              Processing...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4" />
              Accept & Continue
            </>
          )}
        </button>
      </div>
    </div>
  );
}
