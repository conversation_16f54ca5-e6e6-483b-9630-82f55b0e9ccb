import React, { useRef, useEffect } from 'react';

const MicMeter = ({ audioStream }) => {
    const canvasRef = useRef(null);

    useEffect(() => {
      if (audioStream) {
        const canvas = canvasRef.current;
        const canvasContext = canvas.getContext('2d');
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const source = audioContext.createMediaStreamSource(audioStream);
        source.connect(analyser);
        analyser.fftSize = 256; // Adjust FFT size if necessary
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const draw = () => {
          requestAnimationFrame(draw);
          analyser.getByteFrequencyData(dataArray);
          canvasContext.clearRect(0, 0, canvas.width, canvas.height);
          canvasContext.fillStyle = 'rgb(0, 255, 0)';
          const barWidth = canvas.width / bufferLength;
          dataArray.forEach((value, index) => {
            const barHeight = value;
            canvasContext.fillRect(index * barWidth, canvas.height - barHeight, barWidth, barHeight);
          });
        };
  
        draw();
  
        return () => {
          audioContext.close();
        };
      }
    }, [audioStream]);
  
    return (
      <div>
        <canvas ref={canvasRef} width="300" height="100"></canvas>
        <p>Microphone Level</p>
      </div>
    );
};

export default MicMeter;
