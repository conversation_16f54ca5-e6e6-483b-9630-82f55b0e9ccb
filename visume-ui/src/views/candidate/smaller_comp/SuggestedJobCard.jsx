import React, { useState } from 'react';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Building, 
  Users, 
  ExternalLink,
  Bookmark,
  BookmarkCheck,
  Star
} from 'lucide-react';

const SuggestedJobCard = ({ job, iconUrl }) => {
  const [isBookmarked, setIsBookmarked] = useState(false);

  // Mock additional data for better UI (replace with actual job data)
  const jobData = {
    salary: job.salary || '$60,000 - $80,000',
    location: job.location || 'San Francisco, CA',
    type: job.type || 'Full-time',
    experience: job.experience || '2-4 years',
    posted: job.posted || '2 days ago',
    skills: job.skills || ['React', 'Node.js', 'JavaScript'],
    match: job.match || 85,
    ...job
  };

  const handleBookmark = (e) => {
    e.stopPropagation();
    setIsBookmarked(!isBookmarked);
  };

  const handleApply = (e) => {
    e.stopPropagation();
    window.open(job.url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div 
      className="group relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer"
      onClick={handleApply}
    >
      {/* Header */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start gap-3 min-w-0 flex-1">
            <img 
              src={iconUrl || "https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&h=256&q=80"} 
              alt="Company Logo" 
              className="w-10 h-10 rounded-lg object-cover border border-gray-200 dark:border-gray-600 flex-shrink-0" 
            />
            <div className="min-w-0 flex-1">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-1 line-clamp-2">
                {jobData.title}
              </h3>
              <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 mb-2">
                <Building className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{jobData.company}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            {/* Match Badge */}
            <div className="flex items-center gap-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-2 py-1 rounded-full text-xs font-medium">
              <Star className="h-2.5 w-2.5 fill-current" />
              {jobData.match}%
            </div>
            
            {/* Bookmark */}
            <button
              onClick={handleBookmark}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors opacity-0 group-hover:opacity-100"
            >
              {isBookmarked ? (
                <BookmarkCheck className="h-3 w-3 text-blue-600 dark:text-blue-400" />
              ) : (
                <Bookmark className="h-3 w-3 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Job Details */}
        <div className="space-y-1.5 mb-3">
          <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
            <MapPin className="h-3 w-3 flex-shrink-0" />
            <span className="truncate">{jobData.location}</span>
            <span className="text-gray-400">•</span>
            <span>{jobData.type}</span>
          </div>
          
          <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
            <DollarSign className="h-3 w-3 flex-shrink-0" />
            <span className="truncate">{jobData.salary}</span>
          </div>

          <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
            <Users className="h-3 w-3 flex-shrink-0" />
            <span>{jobData.experience}</span>
            <span className="text-gray-400">•</span>
            <span>{jobData.openings} openings</span>
          </div>
        </div>

        {/* Skills */}
        {jobData.skills && jobData.skills.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {jobData.skills.slice(0, 2).map((skill, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                >
                  {skill}
                </span>
              ))}
              {jobData.skills.length > 2 && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                  +{jobData.skills.length - 2}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {jobData.posted}
          </div>
          
          <button
            onClick={handleApply}
            className="inline-flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md text-xs font-medium transition-colors duration-200"
          >
            Apply
            <ExternalLink className="h-2.5 w-2.5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuggestedJobCard;
