// src/views/candidate/CreateVideoProfile.jsx
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import DeviceTest from "./DeviceTest/DeviceTest";
import { HiVideoCamera, <PERSON><PERSON><PERSON><PERSON>, HiShieldCheck } from "react-icons/hi";
import { Video, CheckCircle, Shield, ArrowRight } from "lucide-react";

function CreateVideoProfile() {
  const { videoResumeId: videoProfileId } = useParams();
  const navigate = useNavigate();
  const [userConsent, setUserConsent] = useState(false);

  useEffect(() => {
    if (!videoProfileId) {
      // Redirect to the candidate route if no video profile ID is found
      navigate("/candidate");
    }
  }, [videoProfileId, navigate]);

  const handleProceed = () => {
    // Implement device tests and transitions
    console.log("Proceeding to interview");
  };

  return (
    <div className="p-4 space-y-8">
      {!userConsent ? (
        /* Enhanced Consent Form */
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm mb-8">
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-lg">
                  <HiVideoCamera className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Create Video Resume
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Review and accept terms before proceeding
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Consent Content */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Terms & Consent
                </h2>
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                  Please review and accept our terms to create your video resume. Your privacy and data security are our top priorities.
                </p>
              </div>

              {/* Terms List */}
              <div className="space-y-4 mb-8">
                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                      Video Recording Permission
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      We will access your camera and microphone to record your video resume. This data is securely stored and only used for recruitment purposes.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                      Data Privacy & Security
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Your video resume will be encrypted and stored securely. Only authorized employers you choose to share with can access your content.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                      Content Guidelines
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Please ensure your video content is professional and appropriate. Inappropriate content may result in account suspension.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => navigate("/candidate")}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setUserConsent(true)}
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  I Accept & Continue
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <DeviceTest onStart={handleProceed} />
      )}
    </div>
  );
}

export default CreateVideoProfile;
