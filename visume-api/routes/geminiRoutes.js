// visume-api/routes/geminiRoutes.js
const express = require("express");
const router = express.Router();
const axios = require("axios");


router.post("/assist", async (req, res) => {
  try {
    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: "Prompt is required" });
    }
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GOOGLE_API_KEY}`;
    const payload = {
      contents: [{ role: "user", parts: [{ text: prompt }] }],
    };
    const response = await axios.post(geminiUrl, payload, {
      headers: { "Content-Type": "application/json" },
    });
    res.json(response.data);
  } catch (err) {
    console.error("Gemini API error:", err.message, err?.response?.data);
    res.status(500).json({ error: "Gemini API error", details: err?.response?.data || err.message });
  }
});

module.exports = router;