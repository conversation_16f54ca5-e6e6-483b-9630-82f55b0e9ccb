

# 🚀 ZoomJobs API


## 📋 Prerequisites
Before getting started, make sure you have the following installed:

- **[Node.js](https://nodejs.org/)** (version 14.x or higher)
- **[RabbitMQ Installation](https://www.c-sharpcorner.com/article/how-to-install-and-setup-rabbitmq-server-locally/)** (follow this for RabbitMQ installation, erlang included.)
- **MySQL Database** (configured as per the `config/db.js` file)

## ⚙️ Setup

### 1. Clone the Repository
Clone the repository to your local machine:

```bash
git clone https://github.com/yourusername/repository-name.git
cd repository-name
```

### 2. Install Dependencies
Install the required dependencies by running:

```bash
npm install
```

### 3. Configure the Database
Ensure you have a MySQL database named `zoomjobs` created, with the necessary tables (like `jobseeker`) set up. Adjust your MySQL connection settings in `backend/config/db.js` if necessary.

### 4. Start RabbitMQ
Start your RabbitMQ server:

```bash
rabbitmq-server
```

### 5. Start the Application Concurrently
Run the main application and the worker queue simultaneously with:

```bash
npm run start:all
```

This command will launch the Express application and the resume processing worker.

### 6. 📤 Usage
To register a job seeker and upload their resume and profile picture, send a `POST` request to:

```http
/api/v1/register-jobseeker
```

with the file fields `resume` and `profile_picture`.

Ensure the files are handled correctly by the `multer` storage configured in `backend/routes/authRoutes.js`.

### 7. 📊 Monitoring
Keep an eye on the RabbitMQ management dashboard (if enabled) at:

[http://localhost:15672/](http://localhost:15672/)

to monitor the status of your queues.

## 🛠 Troubleshooting
If you run into issues:

- 🐇 **RabbitMQ**: Check the RabbitMQ logs for any connection issues.
- 🛢 **MySQL**: Ensure the MySQL database is accessible and properly configured.
- 🖥 **Logs**: Review the console logs for errors during resume processing.
