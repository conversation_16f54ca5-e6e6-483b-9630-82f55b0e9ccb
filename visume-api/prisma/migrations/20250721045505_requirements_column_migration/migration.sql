-- AlterTable
ALTER TABLE `videoprofile` ADD COLUMN `requirements` LONGTEXT NULL;

-- CreateTable
CREATE TABLE `mock_interview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cand_id` VARCHAR(20) NULL,
    `mock_interview_id` BIGINT NULL,
    `role` VARCHAR(255) NULL,
    `skills` TEXT NULL,
    `job_type` ENUM('startup', 'mnc', 'mid_range') NULL,
    `experience_range` ENUM('0-1', '2-3', '3-5') NULL,
    `salary` LONGTEXT NULL,
    `questions` LONGTEXT NULL,
    `score` LONGTEXT NULL,
    `status` ENUM('active', 'inactive', 'start') NULL,
    `video_url` VARCHAR(255) NULL,
    `created_at` TIMESTAMP(0) NULL,

    INDEX `cand_id`(`cand_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE `mock_interview` ADD CONSTRAINT `mock_interview_ibfk_1` FOREIGN KEY (`cand_id`) REFERENCES `jobseeker`(`cand_id`) ON DELETE CASCADE ON UPDATE RESTRICT;
