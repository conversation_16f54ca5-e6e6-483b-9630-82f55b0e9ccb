// Employer Profile Management Functions

const prisma = require("../../config/prisma");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    const profiles = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
    });

    if (!profiles || profiles.length === 0) {
      return res
        .status(404)
        .json({ message: "No profiles found for this employer." });
    }

    res.status(200).json({
      message: "Profiles fetched successfully.",
      data: profiles,
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Get employer profile plan and candidate counts
exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(emp_id) },
      select: { plan_id: true, creditsLeft: true, end_date: true },
    });

    if (!employerPlan) {
      return res.status(404).json({
        message: "No employer profile plan found. Please log in again.",
      });
    }

    const planDetails = await prisma.plans.findUnique({
      where: { id: employerPlan.plan_id },
    });

    if (!planDetails) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
      select: { emp_name: true, profile_picture: true },
    });

    if (!employer) {
      return res.status(404).json({
        message: "Employer not found. Please log in again.",
      });
    }

    const shortlisted_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "shortlisted" },
    });

    const unlocked_count = await prisma.employerprofiles.count({
      where: { emp_id: Number(emp_id), status: "unlocked" },
    });

    res.status(200).json({
      message: "Employer profile plan fetched successfully.",
      data: {
        ...planDetails,
        emp_name: employer.emp_name,
        profile_picture: employer.profile_picture,
        creditsLeft: employerPlan.creditsLeft || 0,
        end_date: employerPlan.end_date || 0,
        candidate_counts: {
          shortlisted_count,
          unlocked_count,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Get employer and company details
exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) },
    });

    if (!employer) {
      return res.status(404).json({
        message: "No employer details Found. Please log in again.",
      });
    }

    // Only fetch company if company_id is not null/undefined
    let company = null;
    if (employer.company_id !== null && employer.company_id !== undefined) {
      company = await prisma.company.findUnique({
        where: { id: employer.company_id },
      });
    }

    if (!company) {
      return res.status(404).json({
        message: "No plan details found for the employer profile.",
      });
    }

    res.status(200).json({
      message: "Employer profile data fetched successfully.",
      data: {
        ...employer,
        ...company,
      },
    });
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};