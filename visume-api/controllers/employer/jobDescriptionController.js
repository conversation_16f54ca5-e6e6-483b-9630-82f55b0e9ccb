// Job Description Handling Functions

const prisma = require("../../config/prisma");
const fs = require("fs");
const { jobDescStripper } = require("../../utils/helpers");
const { helperFilterCandidates } = require("../videoProfileController");

exports.uploadJobDescription = async (req, res) => {
  const { emp_id } = req.params;

  const jobDesc = req.files?.job_description
    ? req.files.job_description[0]
    : null;

  if (!jobDesc) {
    return res.status(400).json({ message: "Job description is required." });
  }

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }
  try {
    // Check if Employer ID is valid
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) }
    });

    if (!employer) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Load the uploaded PDF file
    const pdfBuffer = fs.readFileSync(jobDesc.path);
    // Basic PDF validation: check header
    const isValidPDF = pdfBuffer.slice(0, 4).toString() === '%PDF';
    if (!isValidPDF) {
      return res.status(400).json({ message: "Uploaded file is not a valid PDF." });
    }

    // Process the PDF and fetch AI-enhanced data
    console.log("Fetching data from AI...");
    const enhancedData = await jobDescStripper(pdfBuffer);
    console.log("Data Fetched");

    // Send the processed response
    res.status(200).json({
      JobDescription: enhancedData,
      filePath: jobDesc.path,
      message: "Job Description Uploaded Successfully.",
    });
  } catch (error) {
    console.error("Error processing job description:", error);
    let errorMsg = "Failed to process job description.";
    if (error.message && error.message.includes("XRef")) {
      errorMsg = "PDF parsing error: The uploaded file may be corrupted, encrypted, or not supported. Please try uploading a different PDF.";
    }
    return res
      .status(500)
      .json({ message: errorMsg, details: error.message || error });
  }
};

exports.createJobDescription = async (req, res) => {
  const { emp_id, role, skills, experience, location, filePath } = req.body;

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    // Check if Employer ID is valid
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) }
    });

    if (!employer) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Call helper function to filter candidates
    const candidates = await helperFilterCandidates({
      preferred_location: location,
      role,
      selectedSkills: skills,
      experience,
    });

    // Extract video profile IDs from the filtered candidates
    const suggested_profiles = candidates.map((e) => e.video_profile_id);

    // Create job description object as JS object (not stringified)
    const JobDescription = {
      role,
      skills,
      experience,
      location,
      filePath,
    };

    // Convert all BigInt values in suggested_profiles before stringifying
    function convertBigIntDeep(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigIntDeep);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigIntDeep(v)])
        );
      }
      return obj;
    }
    await prisma.job_descriptions.create({
      data: {
        employer_id: Number(emp_id),
        suggested_profiles: JSON.stringify(convertBigIntDeep(suggested_profiles)),
        JobDescription: JSON.stringify(JobDescription),
      }
    });

    // Convert BigInt values before sending to avoid JSON serialization error
    const convertBigInt = (obj) => {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigInt);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigInt(v)])
        );
      }
      return obj;
    };
    res.json(
      convertBigInt({
        message: "Job Description created successfully."
      })
    );
  } catch (error) {
    console.error("Error creating Job Description:", error);
    res
      .status(500)
      .json({ message: "Failed to create Job Description.", error });
  }
};
/**
 * Get all job descriptions for an employer
 */
exports.getJobDescriptionByEmployer = async (req, res) => {
  const { emp_id } = req.params;
  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }
  try {
    const jdList = await prisma.job_descriptions.findMany({
      where: { employer_id: Number(emp_id) },
      orderBy: { id: 'desc' },
      select: { id: true, JobDescription: true }
    });
    if (!jdList || jdList.length === 0) {
      return res.status(404).json({ message: "No job descriptions found for this employer." });
    }
    const jobDescriptions = jdList.map(jd => {
      let parsedJD = {};
      if (typeof jd.JobDescription === "string") {
        try {
          parsedJD = JSON.parse(jd.JobDescription);
        } catch {
          parsedJD = {};
        }
      } else if (typeof jd.JobDescription === "object" && jd.JobDescription !== null) {
        parsedJD = jd.JobDescription;
      }
      return {
        _id: jd.id,
        ...parsedJD,
      };
    });
    res.json({
      jobDescriptions
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch job descriptions.", error });
  }
};

/**
 * Delete a job description by ID
 */
exports.deleteJobDescriptionById = async (req, res) => {
  const { id } = req.params;
  if (!id) {
    return res.status(400).json({ message: "Job Description ID is required." });
  }
  try {
    const result = await prisma.job_descriptions.deleteMany({
      where: { id: Number(id) }
    });
    if (result.count === 0) {
      return res.status(404).json({ message: "Job description not found." });
    }
    res.json({ message: "Job description deleted successfully." });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete job description.", error });
  }
};