const prisma = require("../config/prisma");

// Create a new jobseeker plan
exports.createJobSeekerPlan = async (req, res) => {
  const { name, features } = req.body;

  if (!name || !features) {
    return res.status(400).json({ message: "Name and features are required." });
  }

  try {
    const newPlan = await prisma.jobseekerplans.create({
      data: {
        plan_name: name,
        plan_features: features,
      },
    });

    res.status(201).json({
      message: "Jobseeker plan created successfully.",
      data: {
        id: newPlan.id,
        name: newPlan.plan_name,
        features: newPlan.plan_features,
      },
    });
  } catch (error) {
    console.error("Error creating jobseeker plan:", error);
    res.status(500).json({ message: "Failed to create jobseeker plan." });
  }
};

// Fetch jobseeker plan by candidate ID
exports.getJobSeekerPlanByCandidateId = async (req, res) => {
  const { cand_id } = req.params;

  try {
    const plan = await prisma.jobseekerplans.findFirst({
      where: { cand_id: Number(cand_id) },
    });

    if (!plan) {
      return res
        .status(404)
        .json({ message: "Job seeker plan not found." });
    }

    res.status(200).json({
      message: "Job seeker plan fetched successfully.",
      data: plan,
    });
  } catch (error) {
    console.error("Error fetching job seeker plan:", error);
    res.status(500).send("Failed to fetch job seeker plan.");
  }
};
