const prisma = require("../config/prisma");

// Register company
exports.addNewCompany = async (req, res) => {
  const { company_name, company_description, company_website, gst } = req.body;

  // Check if files were uploaded by Multer
  const company_logo = req.files?.company_logo
    ? req.files.company_logo[0]
    : null;

  if (!company_logo) {
    return res.status(400).json({ message: "Company Logo file is required" });
  }

  try {
    // Check if the company already exists
    const existingCompany = await prisma.company.findUnique({
      where: { company_name },
    });

    if (existingCompany) {
      return res.status(409).json({ message: "Company already exists" });
    }

    // Create new company
    const newCompany = await prisma.company.create({
      data: {
        company_name,
        company_logo: company_logo.filename,
        company_description,
        company_website,
        gst,
      },
    });

    // Return response with company ID
    return res.status(201).json({
      message: "Company registered successfully",
      company_id: newCompany.id,
    });
  } catch (err) {
    console.error("Error during company registration:", err);
    return res.status(500).json({ error: err.message });
  }
};

// Get all companies
exports.getAllCompany = async (req, res) => {
  try {
    const companies = await prisma.company.findMany();

    if (!companies || companies.length === 0) {
      return res.status(404).json({ message: "No companies found" });
    }

    return res.status(200).json(companies);
  } catch (err) {
    console.error("Error retrieving companies:", err);
    return res.status(500).json({ error: err.message });
  }
};
